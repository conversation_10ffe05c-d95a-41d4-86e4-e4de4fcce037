Stack trace:
Frame         Function      Args
0007FFFFBE20  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAD20) msys-2.0.dll+0x1FE8E
0007FFFFBE20  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC0F8) msys-2.0.dll+0x67F9
0007FFFFBE20  000210046832 (000210286019, 0007FFFFBCD8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBE20  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBE20  000210068E24 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC100  00021006A225 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF852280000 ntdll.dll
7FF8504C0000 KERNEL32.DLL
7FF84F990000 KERNELBASE.dll
7FF8508C0000 USER32.dll
7FF84F480000 win32u.dll
7FF850220000 GDI32.dll
7FF84F850000 gdi32full.dll
7FF84F3D0000 msvcp_win.dll
000210040000 msys-2.0.dll
7FF84FED0000 ucrtbase.dll
7FF851210000 advapi32.dll
7FF851590000 msvcrt.dll
7FF850770000 sechost.dll
7FF850100000 RPCRT4.dll
7FF84EA00000 CRYPTBASE.DLL
7FF84F7B0000 bcryptPrimitives.dll
7FF852080000 IMM32.DLL
