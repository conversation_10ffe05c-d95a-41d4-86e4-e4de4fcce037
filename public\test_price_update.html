<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Price Update Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { 
            max-width: 600px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 { 
            color: #333; 
            text-align: center; 
            margin-bottom: 30px;
        }
        .test-section { 
            margin: 25px 0; 
            padding: 20px; 
            border: 2px solid #e0e0e0; 
            border-radius: 10px; 
            background: #fafafa;
        }
        button { 
            padding: 12px 20px; 
            margin: 8px; 
            cursor: pointer; 
            border: none; 
            border-radius: 6px; 
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; transform: translateY(-2px); }
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #218838; transform: translateY(-2px); }
        .btn-info { background: #17a2b8; color: white; }
        .btn-info:hover { background: #138496; transform: translateY(-2px); }
        
        .result { 
            margin: 15px 0; 
            padding: 15px; 
            border-radius: 8px; 
            font-family: 'Courier New', monospace; 
            font-size: 13px;
            line-height: 1.4;
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            border: 2px solid #c3e6cb; 
        }
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            border: 2px solid #f5c6cb; 
        }
        .info { 
            background: #d1ecf1; 
            color: #0c5460; 
            border: 2px solid #bee5eb; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>💰 Price Update Test</h1>
        
        <div class="test-section">
            <h3>Step 1: Login</h3>
            <button onclick="login()" class="btn-success">Login as Admin</button>
            <div id="login-result" class="result info">Click to login first</div>
        </div>
        
        <div class="test-section">
            <h3>Step 2: Add Item to Cart</h3>
            <button onclick="addItem()" class="btn-primary">Add Product 6 (Qty: 2)</button>
            <div id="add-result" class="result info">Add an item to test price updates</div>
        </div>
        
        <div class="test-section">
            <h3>Step 3: Test Price Update</h3>
            <button onclick="updateQuantity()" class="btn-primary">Update to Quantity 5</button>
            <div id="update-result" class="result info">Test if itemTotal is calculated correctly</div>
        </div>
        
        <div class="test-section">
            <h3>Step 4: View Cart Page</h3>
            <button onclick="window.open('/asma/public/cart', '_blank')" class="btn-info">Open Cart Page</button>
            <div class="result info">
                <strong>What to check:</strong><br>
                ✓ Item prices show correctly (not $0.00)<br>
                ✓ Quantity changes update the item total<br>
                ✓ Cart total is calculated correctly
            </div>
        </div>
        
        <div class="test-section">
            <h3>Debug Info</h3>
            <div id="debug-info" class="result info">Test results will appear here</div>
        </div>
    </div>

    <script>
        let debugLog = [];
        
        function logDebug(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.push(`[${timestamp}] ${message}`);
            updateDebugDisplay();
        }
        
        function updateDebugDisplay() {
            const debugDiv = document.getElementById('debug-info');
            debugDiv.innerHTML = debugLog.slice(-3).join('<br>');
        }
        
        async function login() {
            try {
                logDebug('🔄 Logging in...');
                
                const response = await fetch('/asma/public/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: 'email=<EMAIL>&password=admin123'
                });
                
                const data = await response.json();
                const resultDiv = document.getElementById('login-result');
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Login successful`;
                    logDebug('✅ Login successful');
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Login failed: ${data.message}`;
                    logDebug('❌ Login failed');
                }
            } catch (error) {
                logDebug(`💥 Login error: ${error.message}`);
                document.getElementById('login-result').innerHTML = `❌ Error: ${error.message}`;
                document.getElementById('login-result').className = 'result error';
            }
        }
        
        async function addItem() {
            try {
                logDebug('🔄 Adding item...');
                
                const response = await fetch('/asma/public/cart/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: 'product_id=6&quantity=2'
                });
                
                const data = await response.json();
                const resultDiv = document.getElementById('add-result');
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ ${data.message}<br><strong>Cart Count:</strong> ${data.cartCount}`;
                    logDebug('✅ Add item successful');
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ ${data.message}`;
                    logDebug('❌ Add item failed');
                }
            } catch (error) {
                logDebug(`💥 Add item error: ${error.message}`);
                document.getElementById('add-result').innerHTML = `❌ Error: ${error.message}`;
                document.getElementById('add-result').className = 'result error';
            }
        }
        
        async function updateQuantity() {
            try {
                logDebug('🔄 Updating quantity...');
                
                const response = await fetch('/asma/public/cart/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: 'product_id=6&quantity=5'
                });
                
                const data = await response.json();
                const resultDiv = document.getElementById('update-result');
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        ✅ ${data.message}<br>
                        <strong>Cart Count:</strong> ${data.cartCount}<br>
                        <strong>Item Total:</strong> $${data.itemTotal}<br>
                        <strong>Cart Total:</strong> $${data.cartTotal}
                    `;
                    logDebug(`✅ Update successful - Item Total: $${data.itemTotal}`);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ ${data.message}`;
                    logDebug('❌ Update failed');
                }
            } catch (error) {
                logDebug(`💥 Update error: ${error.message}`);
                document.getElementById('update-result').innerHTML = `❌ Error: ${error.message}`;
                document.getElementById('update-result').className = 'result error';
            }
        }
        
        // Initialize
        logDebug('🚀 Price update test loaded');
    </script>
</body>
</html>
