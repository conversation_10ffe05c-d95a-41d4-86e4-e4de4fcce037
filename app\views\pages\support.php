<div class="container mt-5">
    <div class="row">
        <div class="col-lg-8">
            <h1 class="mb-4">
                <i class="fas fa-headset me-2"></i>Customer Support
            </h1>

            <p class="lead mb-4">
                We're here to help! Get in touch with our support team for any questions,
                concerns, or assistance you need.
            </p>

            <?php if (isset($success)): ?>
                <script>
                    window.addEventListener('DOMContentLoaded', function() {
                        showNotification('<?= addslashes(htmlspecialchars($success)) ?>', 'success');
                    });
                </script>
            <?php endif; ?>
            <?php if (isset($error)): ?>
                <script>
                    window.addEventListener('DOMContentLoaded', function() {
                        showNotification('<?= addslashes(htmlspecialchars($error)) ?>', 'error');
                    });
                </script>
            <?php endif; ?>

            <!-- Support Form -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-envelope me-2"></i>Contact Support
                    </h4>
                </div>
                <div class="card-body">
                    <form action="/support" method="POST" id="supportForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name *</label>
                                <input type="text"
                                    class="form-control"
                                    id="first_name"
                                    name="first_name"
                                    value="<?php echo htmlspecialchars($_SESSION['user']['first_name'] ?? ''); ?>"
                                    required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name *</label>
                                <input type="text"
                                    class="form-control"
                                    id="last_name"
                                    name="last_name"
                                    value="<?php echo htmlspecialchars($_SESSION['user']['last_name'] ?? ''); ?>"
                                    required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email"
                                class="form-control"
                                id="email"
                                name="email"
                                value="<?php echo htmlspecialchars($_SESSION['user']['email'] ?? ''); ?>"
                                required>
                        </div>

                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject *</label>
                            <select class="form-select" id="subject" name="subject" required>
                                <option value="">Select a topic</option>
                                <option value="Order Issues">Order Issues</option>
                                <option value="Product Questions">Product Questions</option>
                                <option value="Shipping & Delivery">Shipping & Delivery</option>
                                <option value="Returns & Refunds">Returns & Refunds</option>
                                <option value="Account Issues">Account Issues</option>
                                <option value="Payment Problems">Payment Problems</option>
                                <option value="Technical Support">Technical Support</option>
                                <option value="General Inquiry">General Inquiry</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="order_number" class="form-label">Order Number (if applicable)</label>
                            <input type="text"
                                class="form-control"
                                id="order_number"
                                name="order_number"
                                placeholder="e.g., #12345">
                        </div>

                        <div class="mb-3">
                            <label for="message" class="form-label">Message *</label>
                            <textarea class="form-control"
                                id="message"
                                name="message"
                                rows="6"
                                placeholder="Please describe your issue or question in detail..."
                                required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="priority" class="form-label">Priority Level</label>
                            <select class="form-select" id="priority" name="priority">
                                <option value="low">Low - General question or feedback</option>
                                <option value="medium" selected>Medium - Standard support request</option>
                                <option value="high">High - Urgent issue affecting order</option>
                                <option value="critical">Critical - System error or security concern</option>
                            </select>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="urgent" name="urgent">
                            <label class="form-check-label" for="urgent">
                                This is an urgent matter requiring immediate attention
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Support Information -->
        <div class="col-lg-4">
            <!-- Contact Information -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-phone me-2"></i>Contact Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary">
                            <i class="fas fa-phone me-2"></i>Phone Support
                        </h6>
                        <p class="mb-1">+****************</p>
                        <small class="text-muted">Monday - Friday: 9:00 AM - 6:00 PM EST</small>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-primary">
                            <i class="fas fa-envelope me-2"></i>Email Support
                        </h6>
                        <p class="mb-1"><EMAIL></p>
                        <small class="text-muted">Response within 24 hours</small>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-primary">
                            <i class="fas fa-comments me-2"></i>Live Chat
                        </h6>
                        <p class="mb-1">Available on product pages</p>
                        <small class="text-muted">Real-time assistance</small>
                    </div>
                </div>
            </div>

            <!-- FAQ Quick Links -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Quick Help
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="/help-center" class="list-group-item list-group-item-action">
                            <i class="fas fa-book me-2"></i>Help Center
                        </a>
                        <a href="/shipping-info" class="list-group-item list-group-item-action">
                            <i class="fas fa-truck me-2"></i>Shipping Information
                        </a>
                        <a href="/returns" class="list-group-item list-group-item-action">
                            <i class="fas fa-undo me-2"></i>Returns & Refunds
                        </a>
                        <a href="/privacy-policy" class="list-group-item list-group-item-action">
                            <i class="fas fa-shield-alt me-2"></i>Privacy Policy
                        </a>
                        <a href="/terms-of-service" class="list-group-item list-group-item-action">
                            <i class="fas fa-file-contract me-2"></i>Terms of Service
                        </a>
                    </div>
                </div>
            </div>

            <!-- Response Times -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>Response Times
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-success">Critical Issues</h6>
                        <p class="mb-1">2-4 hours</p>
                        <small class="text-muted">System errors, security concerns</small>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-warning">High Priority</h6>
                        <p class="mb-1">4-8 hours</p>
                        <small class="text-muted">Order issues, payment problems</small>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-info">Medium Priority</h6>
                        <p class="mb-1">24 hours</p>
                        <small class="text-muted">General support requests</small>
                    </div>

                    <div>
                        <h6 class="text-muted">Low Priority</h6>
                        <p class="mb-1">48 hours</p>
                        <small class="text-muted">Feedback, general questions</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.getElementById('supportForm').addEventListener('submit', function(e) {
        const message = document.getElementById('message').value;
        const urgent = document.getElementById('urgent').checked;

        if (message.length < 10) {
            e.preventDefault();
            showNotification('Please provide a detailed message (at least 10 characters).', 'error');
            return;
        }

        if (urgent) {
            if (!confirm('You have marked this as urgent. Are you sure this requires immediate attention?')) {
                e.preventDefault();
                return;
            }
        }

        // Disable submit button to prevent double submission
        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
    });

    // Auto-select priority based on urgent checkbox
    document.getElementById('urgent').addEventListener('change', function() {
        const prioritySelect = document.getElementById('priority');
        if (this.checked) {
            prioritySelect.value = 'critical';
        } else {
            prioritySelect.value = 'medium';
        }
    });
</script>

<style>
    .card {
        border: none;
        border-radius: 15px;
    }

    .card-header {
        border-radius: 15px 15px 0 0 !important;
        border-bottom: 1px solid #dee2e6;
    }

    .btn {
        border-radius: 8px;
        padding: 12px 20px;
        font-weight: 500;
    }

    .form-control,
    .form-select {
        border-radius: 8px;
        border: 1px solid #dee2e6;
        padding: 12px 15px;
    }

    .form-control:focus,
    .form-select:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    .list-group-item {
        border: none;
        border-radius: 8px;
        margin-bottom: 5px;
        padding: 12px 15px;
    }

    .list-group-item:hover {
        background-color: #f8f9fa;
    }

    .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    .btn:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }
</style>