<?php

/**
 * Cart Model
 * Handles all cart-related database operations
 */

class Cart
{
    private $db;

    public function __construct($db)
    {
        $this->db = $db;
    }

    /**
     * Get cart items for user
     */
    public function getItems($userId)
    {
        $sql = "SELECT ci.*, p.name, p.price, p.sale_price, p.slug, p.stock_quantity, c.name as category_name, pi.image_path
                FROM cart_items ci
                JOIN products p ON ci.product_id = p.id
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
                WHERE ci.user_id = :user_id
                ORDER BY ci.created_at DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['user_id' => $userId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Add item to cart
     */
    public function addItem($userId, $productId, $quantity = 1)
    {
        // Check if item already exists
        $existing = $this->getItem($userId, $productId);

        if ($existing) {
            // Update quantity
            return $this->updateQuantity($userId, $productId, $existing['quantity'] + $quantity);
        }

        // Add new item
        $sql = "INSERT INTO cart_items (user_id, product_id, quantity, created_at)
                VALUES (:user_id, :product_id, :quantity, NOW())";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            'user_id' => $userId,
            'product_id' => $productId,
            'quantity' => $quantity
        ]);
    }

    /**
     * Get specific cart item
     */
    public function getItem($userId, $productId)
    {
        $sql = "SELECT * FROM cart_items WHERE user_id = :user_id AND product_id = :product_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            'user_id' => $userId,
            'product_id' => $productId
        ]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Update item quantity
     */
    public function updateQuantity($userId, $productId, $quantity)
    {
        if ($quantity <= 0) {
            return $this->removeItem($userId, $productId);
        }

        $sql = "UPDATE cart_items SET quantity = :quantity, updated_at = NOW()
                WHERE user_id = :user_id AND product_id = :product_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            'user_id' => $userId,
            'product_id' => $productId,
            'quantity' => $quantity
        ]);
    }

    /**
     * Remove item from cart
     */
    public function removeItem($userId, $productId)
    {
        $sql = "DELETE FROM cart_items WHERE user_id = :user_id AND product_id = :product_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            'user_id' => $userId,
            'product_id' => $productId
        ]);
    }

    /**
     * Clear user's cart
     */
    public function clearCart($userId)
    {
        $sql = "DELETE FROM cart_items WHERE user_id = :user_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute(['user_id' => $userId]);
    }

    /**
     * Get cart total
     */
    public function getTotal($userId)
    {
        $sql = "SELECT SUM(ci.quantity * p.price) as total 
                FROM cart_items ci 
                LEFT JOIN products p ON ci.product_id = p.id 
                WHERE ci.user_id = :user_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['user_id' => $userId]);
        return $stmt->fetchColumn() ?: 0;
    }

    /**
     * Get cart item count
     */
    public function getItemCount($userId)
    {
        $sql = "SELECT SUM(quantity) FROM cart_items WHERE user_id = :user_id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['user_id' => $userId]);
        return $stmt->fetchColumn() ?: 0;
    }
}
