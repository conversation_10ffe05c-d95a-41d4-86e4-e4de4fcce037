<section class="auth-section">
    <div class="auth-card">
        <h1>Reset Password</h1>
        <?php if (isset($success)): ?>
            <script>
                window.addEventListener('DOMContentLoaded', function() {
                    showNotification('<?= addslashes(htmlspecialchars($success)) ?>', 'success');
                });
            </script>
        <?php endif; ?>
        <?php if (isset($error)): ?>
            <script>
                window.addEventListener('DOMContentLoaded', function() {
                    showNotification('<?= addslashes(htmlspecialchars($error)) ?>', 'error');
                });
            </script>
        <?php endif; ?>
        <?php if (empty($success)): ?>
            <form method="POST" action="">
                <label>New Password</label>
                <input type="password" name="password" required>
                <label>Confirm Password</label>
                <input type="password" name="confirm_password" required>
                <button type="submit" class="btn btn-primary">Reset Password</button>
            </form>
        <?php endif; ?>
        <div class="auth-links">
            <a href="/login">Back to Login</a>
        </div>
    </div>
</section>
<style>
    .auth-section {
        min-height: 80vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--dark-bg);
    }

    .auth-card {
        background: var(--dark-card);
        padding: 40px 32px;
        border-radius: 12px;
        box-shadow: 0 4px 32px rgba(126, 87, 194, 0.08);
        max-width: 400px;
        width: 100%;
    }

    .auth-card h1 {
        margin-bottom: 18px;
        font-size: 1.7rem;
        color: var(--primary-purple);
    }

    .auth-card label {
        display: block;
        margin-bottom: 6px;
        color: var(--text-primary);
        font-weight: 500;
    }

    .auth-card input {
        width: 100%;
        padding: 10px 12px;
        border-radius: 6px;
        border: 1px solid var(--border-color);
        background: var(--dark-surface);
        color: var(--text-primary);
        margin-bottom: 18px;
    }

    .btn-primary {
        background: var(--primary-purple);
        color: #fff;
        border: none;
        padding: 10px 0;
        border-radius: 6px;
        width: 100%;
        font-weight: 600;
        font-size: 1.1rem;
        cursor: pointer;
    }

    .btn-primary:hover {
        background: #9c27b0;
    }

    .alert {
        padding: 12px 18px;
        border-radius: 8px;
        margin-bottom: 18px;
    }

    .alert-success {
        background: #43a047;
        color: #fff;
    }

    .alert-danger {
        background: #e53935;
        color: #fff;
    }

    .auth-links {
        margin-top: 18px;
        text-align: center;
    }

    .auth-links a {
        color: var(--primary-purple);
        text-decoration: none;
    }
</style>