<!-- Notification Area -->
<div id="notification-area" aria-live="polite" class="mb-3"></div>
<div class="container mt-5">
    <div class="row">
        <!-- Checkout Form -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow-sm mb-4 bg-light border-0">
                <div class="card-header bg-primary text-white py-3">
                    <h4 class="mb-0 fw-bold">
                        <i class="fas fa-credit-card me-2"></i>Checkout
                    </h4>
                </div>
                <div class="card-body p-4">
                    <form action="/checkout" method="POST" id="checkoutForm" autocomplete="off">
                        <!-- Billing Information -->
                        <div class="mb-5 pb-3 border-bottom">
                            <h5 class="mb-4 fw-semibold">
                                <i class="fas fa-user me-2"></i>Billing Information
                            </h5>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="billing_first_name" class="form-label">First Name *</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" class="form-control" id="billing_first_name" name="billing_first_name"
                                            value="<?php echo htmlspecialchars($user['first_name'] ?? ''); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="billing_last_name" class="form-label">Last Name *</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" class="form-control" id="billing_last_name" name="billing_last_name"
                                            value="<?php echo htmlspecialchars($user['last_name'] ?? ''); ?>" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row g-3 mt-2">
                                <div class="col-md-6">
                                    <label for="billing_email" class="form-label">Email Address *</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                        <input type="email" class="form-control" id="billing_email" name="billing_email"
                                            value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="billing_phone" class="form-label">Phone Number</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                        <input type="tel" class="form-control" id="billing_phone" name="billing_phone"
                                            value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <label for="billing_address" class="form-label">Address *</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                    <input type="text" class="form-control" id="billing_address" name="billing_address" required>
                                </div>
                            </div>
                            <div class="row g-3 mt-2">
                                <div class="col-md-6">
                                    <label for="billing_city" class="form-label">City *</label>
                                    <input type="text" class="form-control" id="billing_city" name="billing_city" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="billing_state" class="form-label">State/Province *</label>
                                    <input type="text" class="form-control" id="billing_state" name="billing_state" required>
                                </div>
                            </div>
                            <div class="row g-3 mt-2">
                                <div class="col-md-6">
                                    <label for="billing_postal_code" class="form-label">Postal Code *</label>
                                    <input type="text" class="form-control" id="billing_postal_code" name="billing_postal_code" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="billing_country" class="form-label">Country *</label>
                                    <input type="text" class="form-control" id="billing_country" name="billing_country" required>
                                </div>
                            </div>
                        </div>
                        <!-- Shipping Information -->
                        <div class="mb-5 pb-3 border-bottom">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h5 class="mb-0 fw-semibold">
                                    <i class="fas fa-shipping-fast me-2"></i>Shipping Information
                                </h5>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="same_as_billing" checked>
                                    <label class="form-check-label" for="same_as_billing">
                                        Same as billing address
                                    </label>
                                </div>
                            </div>
                            <div id="shipping-fields" style="display: none;">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="shipping_first_name" class="form-label">First Name *</label>
                                        <input type="text" class="form-control" id="shipping_first_name" name="shipping_first_name">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="shipping_last_name" class="form-label">Last Name *</label>
                                        <input type="text" class="form-control" id="shipping_last_name" name="shipping_last_name">
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <label for="shipping_address" class="form-label">Address *</label>
                                    <input type="text" class="form-control" id="shipping_address" name="shipping_address">
                                </div>
                                <div class="row g-3 mt-2">
                                    <div class="col-md-6">
                                        <label for="shipping_city" class="form-label">City *</label>
                                        <input type="text" class="form-control" id="shipping_city" name="shipping_city">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="shipping_state" class="form-label">State/Province *</label>
                                        <input type="text" class="form-control" id="shipping_state" name="shipping_state">
                                    </div>
                                </div>
                                <div class="row g-3 mt-2">
                                    <div class="col-md-6">
                                        <label for="shipping_postal_code" class="form-label">Postal Code *</label>
                                        <input type="text" class="form-control" id="shipping_postal_code" name="shipping_postal_code">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="shipping_country" class="form-label">Country *</label>
                                        <input type="text" class="form-control" id="shipping_country" name="shipping_country">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Payment Information -->
                        <div class="mb-5 pb-3 border-bottom">
                            <h5 class="mb-4 fw-semibold">
                                <i class="fas fa-credit-card me-2"></i>Payment Information
                            </h5>
                            <div class="mb-3">
                                <label for="card_number" class="form-label">Card Number *</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-credit-card"></i></span>
                                    <input type="text" class="form-control" id="card_number" name="card_number"
                                        placeholder="1234 5678 9012 3456" required maxlength="19">
                                </div>
                            </div>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="card_expiry" class="form-label">Expiry Date *</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                        <input type="text" class="form-control" id="card_expiry" name="card_expiry"
                                            placeholder="MM/YY" required maxlength="5">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="card_cvv" class="form-label">CVV *</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                        <input type="text" class="form-control" id="card_cvv" name="card_cvv"
                                            placeholder="123" required maxlength="4">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3 mt-3">
                                <label for="card_name" class="form-label">Name on Card *</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" class="form-control" id="card_name" name="card_name" required>
                                </div>
                            </div>
                        </div>
                        <!-- Order Notes -->
                        <div class="mb-4">
                            <label for="order_notes" class="form-label">Order Notes (Optional)</label>
                            <textarea class="form-control" id="order_notes" name="order_notes" rows="3"
                                placeholder="Any special instructions or notes for your order..."></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary btn-lg w-100 shadow-sm">
                            <i class="fas fa-lock me-2"></i>Place Order
                        </button>
                    </form>
                </div>
            </div>
        </div>
        <!-- Order Summary -->
        <div class="col-lg-4">
            <div class="card shadow-sm sticky-top" style="top: 30px; z-index: 100;">
                <div class="card-header bg-light py-3">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-shopping-cart me-2"></i>Order Summary
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($cartItems)): ?>
                        <?php foreach ($cartItems as $item): ?>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="d-flex align-items-center">
                                    <img src="<?php echo htmlspecialchars($item['image_path']); ?>"
                                        alt="<?php echo htmlspecialchars($item['name']); ?>"
                                        class="rounded border me-3" style="width: 56px; height: 56px; object-fit: cover;">
                                    <div>
                                        <h6 class="mb-0 fw-semibold"><?php echo htmlspecialchars($item['name']); ?></h6>
                                        <small class="text-muted">Qty: <?php echo $item['quantity']; ?></small>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <div class="fw-bold text-primary fs-6">$<?php echo number_format($item['price'] * $item['quantity'], 2); ?></div>
                                    <?php if ($item['sale_price']): ?>
                                        <small class="text-muted text-decoration-line-through">
                                            $<?php echo number_format($item['original_price'] * $item['quantity'], 2); ?>
                                        </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <hr>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal:</span>
                            <span>$<?php echo number_format($subtotal, 2); ?></span>
                        </div>
                        <?php if (isset($discount) && $discount > 0): ?>
                            <div class="d-flex justify-content-between mb-2 text-success">
                                <span>Discount:</span>
                                <span>-$<?php echo number_format($discount, 2); ?></span>
                            </div>
                        <?php endif; ?>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Shipping:</span>
                            <span>$<?php echo number_format($shipping, 2); ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tax:</span>
                            <span>$<?php echo number_format($tax, 2); ?></span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between fw-bold fs-5 bg-primary bg-opacity-10 rounded p-2 border border-primary">
                            <span>Total:</span>
                            <span class="text-primary">$<?php echo number_format($total, 2); ?></span>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-shopping-cart text-muted mb-3" style="font-size: 3rem;"></i>
                            <h6 class="text-muted">Your cart is empty</h6>
                            <a href="/products" class="btn btn-primary">Continue Shopping</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/5.0.8/jquery.inputmask.min.js"></script>
<script>
    // Notification helper
    function showNotification(message, type = 'info') {
        const area = document.getElementById('notification-area');
        area.innerHTML = `<div class="alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show" role="alert">${message}<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button></div>`;
    }
    // Input masks
    $(function() {
        $('#card_number').inputmask('9999 9999 9999 9999[ 9999 9999]');
        $('#card_expiry').inputmask('99/99');
        $('#billing_phone').inputmask('+9{1,3} ************');
    });
    // Toggle shipping fields
    document.getElementById('same_as_billing').addEventListener('change', function() {
        const shippingFields = document.getElementById('shipping-fields');
        const shippingInputs = shippingFields.querySelectorAll('input');
        if (this.checked) {
            shippingFields.style.display = 'none';
            shippingInputs.forEach(input => input.removeAttribute('required'));
            // Copy billing to shipping
            document.getElementById('shipping_first_name').value = document.getElementById('billing_first_name').value;
            document.getElementById('shipping_last_name').value = document.getElementById('billing_last_name').value;
            document.getElementById('shipping_address').value = document.getElementById('billing_address').value;
            document.getElementById('shipping_city').value = document.getElementById('billing_city').value;
            document.getElementById('shipping_state').value = document.getElementById('billing_state').value;
            document.getElementById('shipping_postal_code').value = document.getElementById('billing_postal_code').value;
            document.getElementById('shipping_country').value = document.getElementById('billing_country').value;
        } else {
            shippingFields.style.display = 'block';
            shippingInputs.forEach(input => input.setAttribute('required', 'required'));
        }
    });
    // Form validation
    document.getElementById('checkoutForm').addEventListener('submit', function(e) {
        const cardNumber = document.getElementById('card_number').value.replace(/\s/g, '');
        const cardExpiry = document.getElementById('card_expiry').value;
        const cardCvv = document.getElementById('card_cvv').value;
        // Basic card validation
        if (cardNumber.length < 13 || cardNumber.length > 19) {
            e.preventDefault();
            showNotification('Please enter a valid card number.', 'error');
            return;
        }
        if (!/^\d{2}\/\d{2}$/.test(cardExpiry)) {
            e.preventDefault();
            showNotification('Please enter expiry date in MM/YY format.', 'error');
            return;
        }
        if (cardCvv.length < 3 || cardCvv.length > 4) {
            e.preventDefault();
            showNotification('Please enter a valid CVV.', 'error');
            return;
        }
        // Disable submit button to prevent double submission
        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    });
</script>

<style>
    body {
        background: #f8fafc;
    }

    .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 2px 16px rgba(0, 0, 0, 0.04);
    }

    .card-header {
        border-radius: 15px 15px 0 0 !important;
        border-bottom: 1px solid #dee2e6;
        background: #f4f7fa;
    }

    .btn {
        border-radius: 8px;
        font-weight: 500;
        transition: box-shadow 0.2s, background 0.2s;
    }

    .btn:hover,
    .btn:focus {
        box-shadow: 0 2px 8px rgba(13, 110, 253, 0.12);
        background: #0b5ed7;
    }

    .form-control {
        border-radius: 8px;
        border: 1px solid #dee2e6;
        padding: 12px 15px;
        background: #fff;
    }

    .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    }

    .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    .btn:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }

    .sticky-top {
        position: sticky !important;
    }

    @media (max-width: 991.98px) {
        .sticky-top {
            position: static !important;
        }

        .card-body,
        .card-header {
            padding-left: 1rem !important;
            padding-right: 1rem !important;
        }
    }

    @media (max-width: 575.98px) {

        .card-body,
        .card-header {
            padding: 1rem !important;
        }

        .btn-lg {
            font-size: 1rem;
            padding: 0.75rem 1rem;
        }
    }
</style>