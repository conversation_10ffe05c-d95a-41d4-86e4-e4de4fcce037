<div class="cart-container">
    <div class="container">
        <div class="cart-header">
            <h1 class="page-title">My Wishlist</h1>
            <p class="cart-subtitle">Items you've saved for later</p>
        </div>

        <?php if (empty($wishlistItems)): ?>
            <div class="empty-cart">
                <div class="empty-cart-icon">
                    <i class="fas fa-heart"></i>
                </div>
                <h2>Your wishlist is empty</h2>
                <p>Start adding products to your wishlist to save them for later.</p>
                <div class="empty-cart-actions">
                    <a href="<?= UrlHelper::url('/products') ?>" class="btn btn-primary btn-lg">
                        <i class="fas fa-shopping-bag"></i>
                        Browse Products
                    </a>
                    <a href="<?= UrlHelper::url('/cart') ?>" class="btn btn-outline btn-lg">
                        <i class="fas fa-shopping-cart"></i>
                        View Cart
                    </a>
                </div>
            </div>
        <?php else: ?>
            <div class="cart-content">
                <div class="cart-items">
                    <div class="cart-items-header">
                        <h3>Wishlist Items (<?= count($wishlistItems) ?>)</h3>
                        <button class="clear-cart-btn" onclick="clearWishlist()">
                            <i class="fas fa-trash"></i>
                            Clear Wishlist
                        </button>
                    </div>

                    <?php foreach ($wishlistItems as $item): ?>
                        <div class="cart-item" data-product-id="<?= $item['id'] ?>">
                            <div class="cart-item-image">
                                <?php if (!empty($item['image_path'])): ?>
                                    <img src="<?= UrlHelper::url('/uploads/products/' . $item['image_path']) ?>"
                                        alt="<?= htmlspecialchars($item['name']) ?>">
                                <?php else: ?>
                                    <img src="<?= UrlHelper::url('/assets/images/default-product.jpg') ?>"
                                        alt="<?= htmlspecialchars($item['name']) ?>">
                                <?php endif; ?>
                            </div>
                            <div class="cart-item-details">
                                <h4 class="cart-item-title">
                                    <a href="<?= UrlHelper::url('/product/' . ($item['slug'] ?? '')) ?>">
                                        <?= htmlspecialchars($item['name']) ?>
                                    </a>
                                </h4>
                                <p class="cart-item-category"><?= htmlspecialchars($item['category_name'] ?? 'Uncategorized') ?></p>

                                <div class="cart-item-price">
                                    <span class="current-price">
                                        $<?= number_format($item['sale_price'] ?: $item['price'], 2) ?>
                                    </span>
                                    <?php if ($item['sale_price']): ?>
                                        <span class="original-price">
                                            $<?= number_format($item['price'], 2) ?>
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <?php if ($item['stock_quantity'] <= 0): ?>
                                    <div class="product-status">
                                        <span class="status out-of-stock">
                                            <i class="fas fa-times-circle"></i>
                                            Out of Stock
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="cart-item-quantity">
                                <?php if ($item['stock_quantity'] > 0): ?>
                                    <div class="quantity-controls">
                                        <button class="quantity-btn" onclick="changeQuantity(<?= $item['id'] ?>, -1)">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <input type="number" class="quantity-input" id="qty-<?= $item['id'] ?>"
                                            value="1" min="1" max="<?= $item['stock_quantity'] ?>"
                                            onchange="updateWishlistQuantity(<?= $item['id'] ?>, this.value)">
                                        <button class="quantity-btn" onclick="changeQuantity(<?= $item['id'] ?>, 1)">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                <?php else: ?>
                                    <span class="out-of-stock-text">Out of Stock</span>
                                <?php endif; ?>
                            </div>

                            <div class="cart-item-total">
                                <span class="item-total">
                                    $<?= number_format(($item['sale_price'] ?: $item['price']), 2) ?>
                                </span>
                            </div>

                            <div class="cart-item-actions">
                                <?php if ($item['stock_quantity'] > 0): ?>
                                    <button class="add-to-cart-btn" onclick="addToCart(<?= $item['id'] ?>, document.getElementById('qty-<?= $item['id'] ?>').value)">
                                        <i class="fas fa-shopping-cart"></i>
                                    </button>
                                <?php endif; ?>
                                <button class="remove-item-btn" onclick="removeFromWishlist(<?= $item['id'] ?>)" title="Remove from wishlist">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div class="cart-summary">
                    <div class="summary-card">
                        <h3 class="summary-title">Wishlist Summary</h3>

                        <div class="summary-items">
                            <div class="summary-item">
                                <span>Total Items</span>
                                <span><?= count($wishlistItems) ?></span>
                            </div>

                            <div class="summary-item">
                                <span>In Stock</span>
                                <span><?= count(array_filter($wishlistItems, fn($item) => $item['stock_quantity'] > 0)) ?></span>
                            </div>

                            <div class="summary-item total-row">
                                <span>Total Value</span>
                                <span class="total-amount">$<?= number_format(array_sum(array_map(fn($item) => $item['sale_price'] ?: $item['price'], $wishlistItems)), 2) ?></span>
                            </div>
                        </div>

                        <div class="summary-actions">
                            <button class="btn btn-primary btn-lg" onclick="addAllToCart()">
                                <i class="fas fa-shopping-cart"></i>
                                Add All to Cart
                            </button>
                            <button class="btn btn-outline btn-lg" onclick="clearWishlist()">
                                <i class="fas fa-trash"></i>
                                Clear Wishlist
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Recommended Products -->
        <?php if (!empty($recommendedProducts)): ?>
            <div class="recommended-section">
                <div class="section-header">
                    <h2><i class="fas fa-lightbulb"></i> You Might Also Like</h2>
                    <p>Discover more products based on your wishlist</p>
                </div>
                <div class="products-grid">
                    <?php foreach ($recommendedProducts as $product): ?>
                        <div class="product-card">
                            <div class="product-image">
                                <a href="<?= UrlHelper::url('/product/' . $product['slug']) ?>" class="image-link">
                                    <img src="<?= UrlHelper::url('uploads/products/' . ($product['primary_image'] ?? 'default.jpg')) ?>"
                                        alt="<?= htmlspecialchars($product['name']) ?>"
                                        class="product-img"
                                        onerror="this.src='<?= UrlHelper::url('images/placeholder-product.jpg') ?>'">
                                    <?php if ($product['sale_price']): ?>
                                        <div class="sale-badge-small">
                                            <span>-<?= round((($product['price'] - $product['sale_price']) / $product['price']) * 100) ?>%</span>
                                        </div>
                                    <?php endif; ?>
                                </a>
                                <div class="product-actions">
                                    <button class="action-btn add-to-wishlist"
                                        onclick="addToWishlist(<?= $product['id'] ?>)"
                                        title="Add to Wishlist">
                                        <i class="fas fa-heart"></i>
                                    </button>
                                    <button class="action-btn add-to-cart"
                                        onclick="addToCart(<?= $product['id'] ?>, 1)"
                                        title="Add to Cart">
                                        <i class="fas fa-shopping-cart"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="product-info">
                                <div class="product-category">
                                    <span><?= htmlspecialchars($product['category_name'] ?? 'Uncategorized') ?></span>
                                </div>
                                <h3 class="product-name">
                                    <a href="<?= UrlHelper::url('/product/' . $product['slug']) ?>">
                                        <?= htmlspecialchars($product['name']) ?>
                                    </a>
                                </h3>
                                <div class="product-price">
                                    <?php if ($product['sale_price']): ?>
                                        <span class="original-price">$<?= number_format($product['price'], 2) ?></span>
                                        <span class="current-price">$<?= number_format($product['sale_price'], 2) ?></span>
                                    <?php else: ?>
                                        <span class="current-price">$<?= number_format($product['price'], 2) ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
    /* Wishlist uses same styles as cart */
    .cart-container {
        padding: var(--spacing-xl) 0;
        background-color: var(--dark-bg);
        min-height: calc(100vh - 200px);
    }

    .cart-header {
        text-align: center;
        margin-bottom: var(--spacing-2xl);
    }

    .page-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .cart-subtitle {
        color: var(--text-secondary);
        font-size: var(--font-size-lg);
    }

    /* Empty Cart/Wishlist */
    .empty-cart {
        text-align: center;
        padding: var(--spacing-2xl) 0;
    }

    .empty-cart-icon {
        font-size: var(--font-size-4xl);
        color: var(--text-muted);
        margin-bottom: var(--spacing-lg);
    }

    .empty-cart h2 {
        font-size: var(--font-size-2xl);
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .empty-cart p {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-xl);
    }

    .empty-cart-actions {
        display: flex;
        gap: var(--spacing-md);
        justify-content: center;
        flex-wrap: wrap;
    }

    /* Cart Content */
    .cart-content {
        display: grid;
        grid-template-columns: 1fr 350px;
        gap: var(--spacing-2xl);
        align-items: start;
    }

    .cart-items {
        background: var(--dark-card);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        border: 1px solid var(--border-color);
    }

    .cart-items-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-xl);
        padding-bottom: var(--spacing-md);
        border-bottom: 1px solid var(--border-color);
    }

    .cart-items-header h3 {
        color: var(--text-primary);
        font-size: var(--font-size-lg);
        font-weight: 600;
    }

    .clear-cart-btn {
        background: transparent;
        border: 1px solid var(--error-color);
        color: var(--error-color);
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--radius-md);
        cursor: pointer;
        transition: all var(--transition-fast);
        font-size: var(--font-size-sm);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .clear-cart-btn:hover {
        background: var(--error-color);
        color: white;
    }

    /* Wishlist Content */
    .wishlist-content {
        margin-bottom: var(--spacing-4xl);
    }

    .wishlist-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-2xl);
        background: var(--dark-card);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-color);
    }

    .wishlist-info h2 {
        color: var(--text-primary);
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-xs);
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .wishlist-info h2 i {
        color: var(--primary-purple);
    }

    .wishlist-info p {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        line-height: 1.5;
    }

    .wishlist-actions {
        display: flex;
        gap: var(--spacing-md);
    }

    /* Wishlist Grid */
    .wishlist-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: var(--spacing-xl);
        margin-bottom: var(--spacing-3xl);
    }

    .wishlist-item {
        background: var(--dark-card);
        border-radius: var(--radius-xl);
        overflow: hidden;
        box-shadow: var(--shadow-lg);
        transition: transform var(--transition-fast), box-shadow var(--transition-fast);
        border: 1px solid var(--border-color);
        height: fit-content;
    }

    .wishlist-item:hover {
        transform: translateY(-6px);
        box-shadow: var(--shadow-xl);
    }

    /* Product Image */
    .product-image {
        position: relative;
        height: 250px;
        overflow: hidden;
    }

    .image-link {
        display: block;
        width: 100%;
        height: 100%;
    }

    .product-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform var(--transition-fast);
    }

    .wishlist-item:hover .product-img {
        transform: scale(1.05);
    }

    .sale-badge {
        position: absolute;
        top: var(--spacing-md);
        left: var(--spacing-md);
        background: var(--error-color);
        color: white;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-md);
        font-weight: 600;
        font-size: var(--font-size-xs);
        box-shadow: var(--shadow-md);
    }

    .quick-actions {
        position: absolute;
        top: var(--spacing-md);
        right: var(--spacing-md);
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);
        opacity: 0;
        transform: translateX(20px);
        transition: all var(--transition-fast);
    }

    .wishlist-item:hover .quick-actions {
        opacity: 1;
        transform: translateX(0);
    }

    .action-btn {
        width: 40px;
        height: 40px;
        border: none;
        border-radius: 50%;
        background: var(--dark-surface);
        color: var(--text-primary);
        cursor: pointer;
        transition: all var(--transition-fast);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: var(--shadow-lg);
        font-size: var(--font-size-sm);
    }

    .action-btn:hover {
        background: var(--primary-purple);
        color: white;
        transform: scale(1.1);
    }

    .action-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    /* Product Info */
    .product-info {
        padding: var(--spacing-xl);
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .product-category {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        color: var(--text-secondary);
        font-size: var(--font-size-xs);
        font-weight: 500;
    }

    .product-category i {
        color: var(--primary-purple);
    }

    .product-name {
        font-size: var(--font-size-md);
        font-weight: 600;
        color: var(--text-primary);
        line-height: 1.4;
        margin: 0;
    }

    .product-name a {
        color: inherit;
        text-decoration: none;
        transition: color var(--transition-fast);
    }

    .product-name a:hover {
        color: var(--primary-purple);
    }

    .product-price {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        flex-wrap: wrap;
    }

    .current-price {
        font-size: var(--font-size-lg);
        font-weight: 700;
        color: var(--primary-purple);
    }

    .original-price {
        font-size: var(--font-size-md);
        color: var(--text-secondary);
        text-decoration: line-through;
    }

    .discount-badge {
        background: var(--success-color);
        color: white;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-sm);
        font-size: var(--font-size-xs);
        font-weight: 600;
    }

    .product-status {
        margin: 0;
    }

    .status {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: var(--font-size-xs);
        font-weight: 600;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-sm);
        background: var(--dark-surface);
    }

    .status.in-stock {
        color: var(--success-color);
        border: 1px solid var(--success-color);
    }

    .status.out-of-stock {
        color: var(--error-color);
        border: 1px solid var(--error-color);
    }

    /* Product Actions */
    .product-actions {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
        margin-top: var(--spacing-sm);
    }

    .quantity-selector {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        padding: var(--spacing-sm);
        background: var(--dark-surface);
        border-radius: var(--radius-md);
        border: 1px solid var(--border-color);
    }

    .quantity-selector label {
        color: var(--text-primary);
        font-weight: 600;
        font-size: var(--font-size-xs);
        min-width: 70px;
    }

    .quantity-controls {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        background: var(--dark-card);
        border-radius: var(--radius-sm);
        padding: var(--spacing-xs);
        border: 1px solid var(--border-color);
    }

    .qty-btn {
        width: 30px;
        height: 30px;
        border: none;
        background: var(--primary-purple);
        color: white;
        border-radius: var(--radius-sm);
        cursor: pointer;
        transition: all var(--transition-fast);
        font-weight: 600;
        font-size: var(--font-size-sm);
    }

    .qty-btn:hover {
        background: var(--primary-light);
        transform: scale(1.05);
    }

    .quantity-controls input {
        width: 50px;
        height: 30px;
        text-align: center;
        border: none;
        background: transparent;
        color: var(--text-primary);
        font-weight: 600;
        font-size: var(--font-size-sm);
    }

    .add-to-cart-btn {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        border: none;
        color: white;
        font-weight: 600;
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
        cursor: pointer;
        transition: all var(--transition-fast);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-xs);
        font-size: var(--font-size-sm);
    }

    .add-to-cart-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .remove-wishlist-btn {
        background: transparent;
        border: 2px solid var(--error-color);
        color: var(--error-color);
        font-weight: 600;
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
        cursor: pointer;
        transition: all var(--transition-fast);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-xs);
        font-size: var(--font-size-sm);
    }

    .remove-wishlist-btn:hover {
        background: var(--error-color);
        color: white;
        transform: translateY(-2px);
    }

    .notify-btn {
        background: transparent;
        border: 2px solid var(--text-secondary);
        color: var(--text-secondary);
        font-weight: 600;
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
        cursor: not-allowed;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-xs);
        font-size: var(--font-size-sm);
    }

    /* Wishlist Summary */
    .wishlist-summary {
        background: var(--dark-card);
        border-radius: var(--radius-xl);
        padding: var(--spacing-2xl);
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-color);
        margin-bottom: var(--spacing-3xl);
    }

    .summary-header h3 {
        color: var(--text-primary);
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-xl);
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .summary-header h3 i {
        color: var(--primary-purple);
    }

    .summary-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-xl);
        margin-bottom: var(--spacing-2xl);
    }

    .stat {
        display: flex;
        align-items: center;
        gap: var(--spacing-lg);
        padding: var(--spacing-lg);
        background: var(--dark-surface);
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-color);
        transition: transform var(--transition-fast);
    }

    .stat:hover {
        transform: translateY(-2px);
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: var(--font-size-lg);
        box-shadow: var(--shadow-md);
    }

    .stat-content {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .stat-value {
        font-size: var(--font-size-xl);
        font-weight: 700;
        color: var(--text-primary);
    }

    .stat-label {
        font-size: var(--font-size-xs);
        color: var(--text-secondary);
        font-weight: 500;
    }

    .summary-actions {
        display: flex;
        gap: var(--spacing-md);
        flex-wrap: wrap;
        justify-content: center;
    }

    /* Recommended Section */
    .recommended-section {
        margin-top: var(--spacing-4xl);
        padding-top: var(--spacing-3xl);
        border-top: 1px solid var(--border-color);
    }

    .section-header {
        text-align: center;
        margin-bottom: var(--spacing-2xl);
    }

    .section-header h2 {
        color: var(--text-primary);
        font-size: var(--font-size-2xl);
        margin-bottom: var(--spacing-sm);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
    }

    .section-header h2 i {
        color: var(--primary-purple);
    }

    .section-header p {
        color: var(--text-secondary);
        font-size: var(--font-size-md);
        line-height: 1.6;
    }

    .products-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-xl);
        max-width: 900px;
        margin: 0 auto;
    }

    .product-card {
        background: var(--dark-card);
        border-radius: var(--radius-xl);
        overflow: hidden;
        box-shadow: var(--shadow-lg);
        transition: transform var(--transition-fast);
        border: 1px solid var(--border-color);
        height: fit-content;
    }

    .product-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }

    .product-card .product-image {
        height: 180px;
        position: relative;
    }

    .product-card .product-info {
        padding: var(--spacing-lg);
        gap: var(--spacing-sm);
    }

    .product-card .product-name {
        font-size: var(--font-size-sm);
        line-height: 1.3;
    }

    .product-card .current-price {
        font-size: var(--font-size-md);
        font-weight: 600;
    }

    .sale-badge-small {
        position: absolute;
        top: var(--spacing-sm);
        left: var(--spacing-sm);
        background: var(--error-color);
        color: white;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-sm);
        font-size: var(--font-size-xs);
        font-weight: 600;
    }

    /* Notification Styles */
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 12px;
        color: white;
        font-weight: 600;
        z-index: 1000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .notification-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
        .wishlist-grid {
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: var(--spacing-lg);
        }

        .products-grid {
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        }
    }

    @media (max-width: 768px) {
        .header-content {
            flex-direction: column;
            text-align: center;
            gap: var(--spacing-lg);
            padding: var(--spacing-xl);
        }

        .wishlist-header {
            flex-direction: column;
            gap: var(--spacing-lg);
            text-align: center;
            padding: var(--spacing-lg);
        }

        .wishlist-actions {
            justify-content: center;
            flex-wrap: wrap;
        }

        .wishlist-grid {
            grid-template-columns: 1fr;
            gap: var(--spacing-lg);
        }

        .summary-stats {
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }

        .summary-actions {
            justify-content: center;
            flex-direction: column;
        }

        .products-grid {
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: var(--spacing-md);
        }

        .quantity-selector {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--spacing-sm);
        }

        .product-actions {
            gap: var(--spacing-sm);
        }
    }

    @media (max-width: 480px) {
        .container {
            padding: 0 var(--spacing-md);
        }

        .header-text h1 {
            font-size: var(--font-size-2xl);
        }

        .wishlist-grid {
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }

        .products-grid {
            grid-template-columns: 1fr;
        }

        .empty-actions {
            flex-direction: column;
        }

        .summary-actions {
            flex-direction: column;
        }

        .product-info {
            padding: var(--spacing-lg);
        }

        .wishlist-summary {
            padding: var(--spacing-lg);
        }
    }
</style>

<script>
    // Add to Cart Function
    function addToCart(productId, quantity) {
        const formData = new FormData();
        formData.append('product_id', productId);
        formData.append('quantity', quantity);

        fetch('<?= UrlHelper::url('/cart/add') ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Product added to cart successfully!', 'success');
                    // Update cart count if you have a cart counter
                    updateCartCount();
                } else {
                    showNotification(data.message || 'Failed to add to cart', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred while adding to cart', 'error');
            });
    }

    // Remove from Wishlist Function
    function removeFromWishlist(productId) {
        if (!confirm('Are you sure you want to remove this item from your wishlist?')) {
            return;
        }

        const formData = new FormData();
        formData.append('product_id', productId);

        fetch('<?= UrlHelper::url('/wishlist/remove') ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Item removed from wishlist', 'success');
                    // Remove the item from the DOM
                    const wishlistItem = document.querySelector(`[data-product-id="${productId}"]`);
                    if (wishlistItem) {
                        wishlistItem.style.opacity = '0';
                        wishlistItem.style.transform = 'scale(0.8)';
                        setTimeout(() => {
                            wishlistItem.remove();
                            updateWishlistCount();
                            checkEmptyWishlist();
                        }, 300);
                    }
                } else {
                    showNotification(data.message || 'Failed to remove from wishlist', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred while removing from wishlist', 'error');
            });
    }

    // Add to Wishlist Function
    function addToWishlist(productId) {
        const formData = new FormData();
        formData.append('product_id', productId);

        fetch('<?= UrlHelper::url('/wishlist/add') ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Product added to wishlist!', 'success');
                    updateWishlistCount();
                } else {
                    showNotification(data.message || 'Failed to add to wishlist', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred while adding to wishlist', 'error');
            });
    }

    // Add All to Cart Function
    function addAllToCart() {
        const inStockItems = document.querySelectorAll('.wishlist-item .add-to-cart-btn:not([disabled])');

        if (inStockItems.length === 0) {
            showNotification('No items in stock to add to cart', 'warning');
            return;
        }

        let addedCount = 0;
        let totalItems = inStockItems.length;

        inStockItems.forEach((btn, index) => {
            const productId = btn.closest('.wishlist-item').dataset.productId;
            const quantityInput = document.getElementById(`qty-${productId}`);
            const quantity = quantityInput ? quantityInput.value : 1;

            setTimeout(() => {
                addToCart(productId, quantity);
                addedCount++;

                if (addedCount === totalItems) {
                    showNotification(`Added ${totalItems} items to cart!`, 'success');
                }
            }, index * 200); // Stagger the requests
        });
    }

    // Clear Wishlist Function
    function clearWishlist() {
        if (!confirm('Are you sure you want to clear your entire wishlist? This action cannot be undone.')) {
            return;
        }

        const wishlistItems = document.querySelectorAll('.wishlist-item');
        const productIds = Array.from(wishlistItems).map(item => item.dataset.productId);

        // Send request to clear all items
        fetch('<?= UrlHelper::url('/wishlist/clear') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    product_ids: productIds
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Wishlist cleared successfully', 'success');
                    // Animate removal of all items
                    wishlistItems.forEach((item, index) => {
                        setTimeout(() => {
                            item.style.opacity = '0';
                            item.style.transform = 'scale(0.8)';
                        }, index * 100);
                    });

                    setTimeout(() => {
                        location.reload(); // Reload to show empty state
                    }, wishlistItems.length * 100 + 500);
                } else {
                    showNotification(data.message || 'Failed to clear wishlist', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred while clearing wishlist', 'error');
            });
    }

    // Change Quantity Function
    function changeQuantity(productId, delta) {
        const input = document.getElementById(`qty-${productId}`);
        const newValue = Math.max(1, Math.min(parseInt(input.value) + delta, parseInt(input.max)));
        input.value = newValue;
    }

    // Share Wishlist Function
    function shareWishlist() {
        if (navigator.share) {
            navigator.share({
                title: 'My Wishlist - Cleanance Lab',
                text: 'Check out my wishlist on Cleanance Lab!',
                url: window.location.href
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(window.location.href).then(() => {
                showNotification('Wishlist link copied to clipboard!', 'success');
            });
        }
    }

    // Check if wishlist is empty
    function checkEmptyWishlist() {
        const wishlistItems = document.querySelectorAll('.wishlist-item');
        if (wishlistItems.length === 0) {
            location.reload(); // Reload to show empty state
        }
    }

    // Update wishlist count (if you have a counter in header)
    function updateWishlistCount() {
        const wishlistItems = document.querySelectorAll('.wishlist-item');
        const count = wishlistItems.length;

        // Update any wishlist counters in the header
        const counters = document.querySelectorAll('.wishlist-count');
        counters.forEach(counter => {
            counter.textContent = count;
        });
    }

    // Update cart count (if you have a counter in header)
    function updateCartCount() {
        // This would typically fetch the current cart count from the server
        // For now, we'll just show a notification
        console.log('Cart updated');
    }

    // Notification System
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            ${getNotificationStyle(type)}
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    function getNotificationIcon(type) {
        switch (type) {
            case 'success':
                return 'check-circle';
            case 'error':
                return 'exclamation-circle';
            case 'warning':
                return 'exclamation-triangle';
            default:
                return 'info-circle';
        }
    }

    function getNotificationStyle(type) {
        switch (type) {
            case 'success':
                return 'background: linear-gradient(135deg, #10b981, #059669);';
            case 'error':
                return 'background: linear-gradient(135deg, #ef4444, #dc2626);';
            case 'warning':
                return 'background: linear-gradient(135deg, #f59e0b, #d97706);';
            default:
                return 'background: linear-gradient(135deg, #3b82f6, #2563eb);';
        }
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        updateWishlistCount();
    });
</script>