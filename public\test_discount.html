<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Discount Code Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { 
            max-width: 600px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 { 
            color: #333; 
            text-align: center; 
            margin-bottom: 30px;
        }
        .test-section { 
            margin: 25px 0; 
            padding: 20px; 
            border: 2px solid #e0e0e0; 
            border-radius: 10px; 
            background: #fafafa;
        }
        button { 
            padding: 12px 20px; 
            margin: 8px; 
            cursor: pointer; 
            border: none; 
            border-radius: 6px; 
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; transform: translateY(-2px); }
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #218838; transform: translateY(-2px); }
        .btn-info { background: #17a2b8; color: white; }
        .btn-info:hover { background: #138496; transform: translateY(-2px); }
        
        .result { 
            margin: 15px 0; 
            padding: 15px; 
            border-radius: 8px; 
            font-family: 'Courier New', monospace; 
            font-size: 13px;
            line-height: 1.4;
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            border: 2px solid #c3e6cb; 
        }
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            border: 2px solid #f5c6cb; 
        }
        .info { 
            background: #d1ecf1; 
            color: #0c5460; 
            border: 2px solid #bee5eb; 
        }
        input[type="text"] {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎫 Discount Code Test</h1>
        
        <div class="test-section">
            <h3>Step 1: Login & Add Items</h3>
            <button onclick="login()" class="btn-success">Login as Admin</button>
            <button onclick="addItem()" class="btn-primary">Add Item to Cart</button>
            <div id="setup-result" class="result info">Setup cart for discount testing</div>
        </div>
        
        <div class="test-section">
            <h3>Step 2: Test Discount Codes</h3>
            <div style="margin: 15px 0;">
                <input type="text" id="discountCode" placeholder="Enter discount code" value="SAVE10">
                <button onclick="testDiscount()" class="btn-primary">Apply Discount</button>
            </div>
            <div id="discount-result" class="result info">
                <strong>Test codes to try:</strong><br>
                • SAVE10 (if exists)<br>
                • WELCOME20 (if exists)<br>
                • INVALID (should fail)
            </div>
        </div>
        
        <div class="test-section">
            <h3>Step 3: View Cart Page</h3>
            <button onclick="window.open('/asma/public/cart', '_blank')" class="btn-info">Open Cart Page</button>
            <div class="result info">Test the discount form directly on the cart page</div>
        </div>
        
        <div class="test-section">
            <h3>Debug Info</h3>
            <div id="debug-info" class="result info">Test results will appear here</div>
        </div>
    </div>

    <script>
        let debugLog = [];
        
        function logDebug(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.push(`[${timestamp}] ${message}`);
            updateDebugDisplay();
        }
        
        function updateDebugDisplay() {
            const debugDiv = document.getElementById('debug-info');
            debugDiv.innerHTML = debugLog.slice(-3).join('<br>');
        }
        
        async function login() {
            try {
                logDebug('🔄 Logging in...');
                
                const response = await fetch('/asma/public/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: 'email=<EMAIL>&password=admin123'
                });
                
                const data = await response.json();
                const resultDiv = document.getElementById('setup-result');
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Login successful`;
                    logDebug('✅ Login successful');
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Login failed: ${data.message}`;
                    logDebug('❌ Login failed');
                }
            } catch (error) {
                logDebug(`💥 Login error: ${error.message}`);
                document.getElementById('setup-result').innerHTML = `❌ Error: ${error.message}`;
                document.getElementById('setup-result').className = 'result error';
            }
        }
        
        async function addItem() {
            try {
                logDebug('🔄 Adding item...');
                
                const response = await fetch('/asma/public/cart/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: 'product_id=6&quantity=3'
                });
                
                const data = await response.json();
                const resultDiv = document.getElementById('setup-result');
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Item added - Cart Count: ${data.cartCount}`;
                    logDebug('✅ Add item successful');
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ ${data.message}`;
                    logDebug('❌ Add item failed');
                }
            } catch (error) {
                logDebug(`💥 Add item error: ${error.message}`);
                document.getElementById('setup-result').innerHTML = `❌ Error: ${error.message}`;
                document.getElementById('setup-result').className = 'result error';
            }
        }
        
        async function testDiscount() {
            try {
                const discountCode = document.getElementById('discountCode').value.trim();
                if (!discountCode) {
                    document.getElementById('discount-result').innerHTML = '❌ Please enter a discount code';
                    document.getElementById('discount-result').className = 'result error';
                    return;
                }
                
                logDebug(`🔄 Testing discount code: ${discountCode}`);
                
                const response = await fetch('/asma/public/cart/apply-discount', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: `discount_code=${encodeURIComponent(discountCode)}`
                });
                
                const data = await response.json();
                const resultDiv = document.getElementById('discount-result');
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        ✅ ${data.message}<br>
                        <strong>Discount Amount:</strong> $${data.discountAmount}<br>
                        <strong>New Cart Total:</strong> $${data.cartTotal}
                    `;
                    logDebug(`✅ Discount applied: $${data.discountAmount}`);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ ${data.message}`;
                    logDebug(`❌ Discount failed: ${data.message}`);
                }
            } catch (error) {
                logDebug(`💥 Discount error: ${error.message}`);
                document.getElementById('discount-result').innerHTML = `❌ Error: ${error.message}`;
                document.getElementById('discount-result').className = 'result error';
            }
        }
        
        // Initialize
        logDebug('🚀 Discount test page loaded');
    </script>
</body>
</html>
