# Cleanance Lab - Developer Quick Reference

## Getting Started

### Prerequisites
- PHP 8.0+
- MySQL 5.7+
- Web server (Apache/Nginx)
- Composer (for dependencies)

### Installation
1. Clone/download the project to your web server directory
2. Run `composer install` to install dependencies
3. Access `http://your-domain/install.php` to set up the database
4. Configure `config/database.php` with your database credentials
5. Set appropriate permissions for `public/uploads/` directory

## Common Development Tasks

### Adding a New Route
Edit `routes/web.php`:
```php
'GET|/new-page' => 'HomeController@newPage',
'POST|/api/new-endpoint' => 'HomeController@newEndpoint',
```

### Creating a New Controller Method
In your controller class:
```php
public function newPage() {
    $data = ['pageTitle' => 'New Page'];
    $this->render('pages/new-page', $data);
}
```

### Creating a New View
Create file in `app/views/` directory:
```php
<?php $pageTitle = 'Page Title'; ?>
<div class="container">
    <h1><?= htmlspecialchars($pageTitle) ?></h1>
    <!-- Your content here -->
</div>
```

### Database Queries
Using PDO in controllers:
```php
$stmt = $this->pdo->prepare("SELECT * FROM products WHERE category_id = ?");
$stmt->execute([$categoryId]);
$products = $stmt->fetchAll();
```

### URL Generation
Using UrlHelper:
```php
UrlHelper::url('/products')           // Application URL
UrlHelper::asset('images/logo.png')   // Asset URL
UrlHelper::css('style.css')           // CSS URL
UrlHelper::js('script.js')            // JS URL
```

## Key Classes and Methods

### BaseController
```php
$this->render($view, $data)           // Render view with layout
$this->renderPartial($view, $data)    // Render view without layout
$this->redirect($url)                 // Redirect to URL
$this->json($data)                    // Return JSON response
$this->getCurrentUser()               // Get current user
$this->requireAuth()                  // Require authentication
```

### User Model
```php
$user = new User($pdo);
$user->create($userData)              // Create new user
$user->findByEmail($email)            // Find user by email
$user->update($id, $data)             // Update user
$user->updatePassword($id, $password) // Change password
```

### Product Model
```php
$product = new Product($pdo);
$product->getAll($filters, $limit, $offset)  // Get products with filters
$product->getFeatured($limit)               // Get featured products
$product->findById($id)                     // Get product by ID
$product->search($query, $limit)            // Search products
```

### Cart Model
```php
$cart = new Cart($pdo);
$cart->getItems($userId)              // Get cart items
$cart->addItem($userId, $productId, $qty)    // Add to cart
$cart->updateItem($userId, $productId, $qty) // Update quantity
$cart->removeItem($userId, $productId)       // Remove from cart
```

## Database Schema Quick Reference

### Key Tables
- **users**: User accounts and profiles
- **products**: Product catalog
- **categories**: Product categories
- **orders**: Customer orders
- **cart_items**: Shopping cart
- **product_reviews**: Product reviews
- **discount_codes**: Promotional codes

### Common Relationships
- products.category_id → categories.id
- orders.user_id → users.id
- cart_items.user_id → users.id
- cart_items.product_id → products.id

## Security Best Practices

### Input Validation
```php
$email = filter_var($_POST['email'], FILTER_VALIDATE_EMAIL);
$name = htmlspecialchars(trim($_POST['name']));
```

### Database Queries
Always use prepared statements:
```php
$stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
$stmt->execute([$email]);
```

### Authentication Check
```php
$this->requireAuth();  // In controller methods
if (!$this->user) {    // Manual check
    $this->redirect('login');
}
```

## Configuration

### App Settings (`config/app.php`)
```php
define('APP_NAME', 'Your App Name');
define('DEBUG_MODE', false);  // Set to false in production
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024);  // 5MB
```

### Database Settings (`config/database.php`)
```php
return [
    'host' => 'localhost',
    'dbname' => 'your_database',
    'username' => 'your_username',
    'password' => 'your_password',
];
```

## Common File Locations

### Adding CSS/JS
- CSS files: `public/assets/css/`
- JS files: `public/assets/js/`
- Images: `public/assets/img/`

### Upload Directories
- Product images: `public/uploads/products/`
- User files: `public/uploads/users/`

### View Templates
- Public pages: `app/views/home/<USER>
- Admin pages: `app/views/admin/`
- Auth pages: `app/views/auth/`

## Debugging

### Error Logging
```php
error_log("Debug message: " . $variable);
```

### Debug Mode
Set `DEBUG_MODE = true` in `config/app.php` to see detailed errors.

### Database Debugging
```php
try {
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
}
```

## API Endpoints

### Cart API
- `POST /api/cart/add` - Add item to cart
- `POST /api/cart/update` - Update cart item
- `POST /api/cart/remove` - Remove cart item

### Wishlist API
- `POST /api/wishlist/add` - Add to wishlist
- `POST /api/wishlist/remove` - Remove from wishlist

### Product API
- `GET /api/products/search` - Search products
- `GET /api/categories` - Get categories

## Email Configuration

### SMTP Settings
Configure in `config/app.php`:
```php
define('SMTP_HOST', 'your-smtp-host');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-password');
```

## File Upload Handling

### Allowed File Types
Configured in `config/app.php`:
```php
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
```

### Upload Directory
Files are uploaded to `public/uploads/` with subdirectories for different types.

## Performance Tips

1. Use pagination for large datasets
2. Implement proper database indexing
3. Optimize images before upload
4. Use prepared statements for database queries
5. Enable caching for static assets
6. Minimize database queries in loops

## Common Issues

### File Permissions
Ensure `public/uploads/` is writable:
```bash
chmod 755 public/uploads/
```

### Database Connection
Check `config/database.php` settings and ensure MySQL is running.

### Missing Dependencies
Run `composer install` to install required packages.

### URL Routing
Check `routes/web.php` for correct route definitions.
