<!-- Enhanced Hero Section -->
<section class="hero">
    <div class="hero-background">
        <div class="hero-particles"></div>
        <div class="hero-gradient-overlay"></div>
    </div>

    <div class="container">
        <div class="hero-content">
            <div class="hero-text">
                <div class="hero-badge">
                    <i class="fas fa-star"></i>
                    <span>Premium Quality Products</span>
                </div>

                <h1 class="hero-title">
                    <span class="hero-title-main">Transform Your World with</span>
                    <span class="hero-title-accent">Cleanance Lab</span>
                </h1>

                <p class="hero-subtitle">
                    Discover cutting-edge products crafted with precision and care. From innovative solutions to everyday essentials, we bring you the finest quality that elevates your lifestyle.
                </p>

                <div class="hero-stats">
                    <div class="hero-stat">
                        <div class="hero-stat-number">10K+</div>
                        <div class="hero-stat-label">Happy Customers</div>
                    </div>
                    <div class="hero-stat">
                        <div class="hero-stat-number">500+</div>
                        <div class="hero-stat-label">Premium Products</div>
                    </div>
                    <div class="hero-stat">
                        <div class="hero-stat-number">99%</div>
                        <div class="hero-stat-label">Satisfaction Rate</div>
                    </div>
                </div>

                <div class="hero-actions">
                    <a href="<?= UrlHelper::url('/products') ?>" class="btn btn-primary btn-lg hero-cta-primary">
                        <i class="fas fa-shopping-bag"></i>
                        <span>Explore Products</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                    <a href="<?= UrlHelper::url('/about') ?>" class="btn btn-outline btn-lg hero-cta-secondary">
                        <i class="fas fa-play-circle"></i>
                        <span>Watch Story</span>
                    </a>
                </div>

                <div class="hero-features">
                    <div class="hero-feature">
                        <i class="fas fa-shipping-fast"></i>
                        <span>Free Shipping</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-shield-alt"></i>
                        <span>Secure Payment</span>
                    </div>
                    <div class="hero-feature">
                        <i class="fas fa-medal"></i>
                        <span>Premium Quality</span>
                    </div>
                </div>
            </div>

            <div class="hero-visual-section">
                <div class="hero-main-visual">
                    <div class="hero-product-showcase">
                        <div class="hero-product-card hero-product-1">
                            <div class="product-image">
                                <i class="fas fa-flask"></i>
                            </div>
                            <div class="product-info">
                                <span class="product-name">Lab Essentials</span>
                                <span class="product-price">$29.99</span>
                            </div>
                        </div>

                        <div class="hero-product-card hero-product-2">
                            <div class="product-image">
                                <i class="fas fa-vial"></i>
                            </div>
                            <div class="product-info">
                                <span class="product-name">Premium Solutions</span>
                                <span class="product-price">$49.99</span>
                            </div>
                        </div>

                        <div class="hero-product-card hero-product-3">
                            <div class="product-image">
                                <i class="fas fa-microscope"></i>
                            </div>
                            <div class="product-info">
                                <span class="product-name">Research Tools</span>
                                <span class="product-price">$89.99</span>
                            </div>
                        </div>
                    </div>

                    <div class="hero-floating-elements">
                        <div class="floating-element element-1">
                            <i class="fas fa-atom"></i>
                        </div>
                        <div class="floating-element element-2">
                            <i class="fas fa-dna"></i>
                        </div>
                        <div class="floating-element element-3">
                            <i class="fas fa-molecular"></i>
                        </div>
                        <div class="floating-element element-4">✨</div>
                        <div class="floating-element element-5">🧪</div>
                        <div class="floating-element element-6">⚗️</div>
                    </div>
                </div>

                <div class="hero-trust-indicators">
                    <div class="trust-indicator">
                        <i class="fas fa-certificate"></i>
                        <span>ISO Certified</span>
                    </div>
                    <div class="trust-indicator">
                        <i class="fas fa-award"></i>
                        <span>Award Winning</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="hero-scroll-indicator">
        <div class="scroll-arrow">
            <i class="fas fa-chevron-down"></i>
        </div>
        <span>Discover More</span>
    </div>
</section>

<!-- Visual Separator -->
<div class="section-separator-glow"></div>

<!-- Enhanced Categories Section -->
<section class="categories-section section-enhanced" id="categoriesSection">
    <div class="container-enhanced">
        <div class="section-header-enhanced" data-aos="fade-up">
            <div class="section-badge">
                <i class="fas fa-th-large"></i>
                <span>Product Categories</span>
            </div>
            <h2 class="section-title">Discover Our Laboratory Solutions</h2>
            <p class="section-subtitle">
                From cutting-edge equipment to essential supplies, explore our comprehensive range of
                <span class="highlight-text">professional-grade laboratory products</span> designed for precision and reliability.
            </p>
            <div class="section-stats">
                <div class="stat-item">
                    <span class="stat-number">8+</span>
                    <span class="stat-label">Categories</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">850+</span>
                    <span class="stat-label">Products</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">99.8%</span>
                    <span class="stat-label">Quality Rating</span>
                </div>
            </div>
        </div>

        <div class="categories-grid" data-aos="fade-up" data-aos-delay="200">
            <?php
            $labCategories = [
                [
                    'name' => 'Laboratory Equipment',
                    'slug' => 'equipment',
                    'description' => 'Precision instruments and advanced analytical equipment for professional research',
                    'icon' => 'fas fa-microscope',
                    'color' => '#7E57C2',
                    'products_count' => 150,
                    'featured' => true
                ],
                [
                    'name' => 'Chemicals & Reagents',
                    'slug' => 'chemicals',
                    'description' => 'High-purity chemicals and reagents for accurate experimental results',
                    'icon' => 'fas fa-flask',
                    'color' => '#2196F3',
                    'products_count' => 200,
                    'featured' => false
                ],
                [
                    'name' => 'Glassware & Plasticware',
                    'slug' => 'glassware',
                    'description' => 'Premium laboratory glassware and plastic consumables',
                    'icon' => 'fas fa-vial',
                    'color' => '#4CAF50',
                    'products_count' => 120,
                    'featured' => false
                ],
                [
                    'name' => 'Safety Equipment',
                    'slug' => 'safety',
                    'description' => 'Essential safety gear and protective equipment for laboratory work',
                    'icon' => 'fas fa-shield-alt',
                    'color' => '#FF9800',
                    'products_count' => 80,
                    'featured' => false
                ],
                [
                    'name' => 'Measurement Tools',
                    'slug' => 'measurement',
                    'description' => 'Precision measurement and calibration instruments',
                    'icon' => 'fas fa-ruler-combined',
                    'color' => '#E91E63',
                    'products_count' => 90,
                    'featured' => false
                ],
                [
                    'name' => 'Sample Preparation',
                    'slug' => 'sample-prep',
                    'description' => 'Tools and equipment for sample preparation and processing',
                    'icon' => 'fas fa-test-tube',
                    'color' => '#9C27B0',
                    'products_count' => 110,
                    'featured' => false
                ],
                [
                    'name' => 'Storage Solutions',
                    'slug' => 'storage',
                    'description' => 'Specialized storage systems for laboratory materials',
                    'icon' => 'fas fa-archive',
                    'color' => '#607D8B',
                    'products_count' => 60,
                    'featured' => false
                ],
                [
                    'name' => 'Research Kits',
                    'slug' => 'research-kits',
                    'description' => 'Complete research kits and educational laboratory sets',
                    'icon' => 'fas fa-box-open',
                    'color' => '#795548',
                    'products_count' => 40,
                    'featured' => false
                ]
            ];

            foreach ($labCategories as $index => $category): ?>
                <div class="category-card <?= $category['featured'] ? 'featured' : '' ?>"
                    data-aos="zoom-in"
                    data-aos-delay="<?= 300 + ($index * 100) ?>"
                    data-category="<?= $category['slug'] ?>">
                    <a href="<?= UrlHelper::url('/products/' . $category['slug']) ?>" class="category-link">
                        <div class="category-background">
                            <div class="category-pattern"></div>
                            <div class="category-glow" style="background: <?= $category['color'] ?>20;"></div>
                        </div>

                        <div class="category-content">
                            <div class="category-icon" style="color: <?= $category['color'] ?>;">
                                <i class="<?= $category['icon'] ?>"></i>
                                <div class="icon-glow" style="background: <?= $category['color'] ?>40;"></div>
                            </div>

                            <div class="category-info">
                                <h3 class="category-name"><?= htmlspecialchars($category['name']) ?></h3>
                                <p class="category-description"><?= htmlspecialchars($category['description']) ?></p>

                                <div class="category-meta">
                                    <span class="products-count">
                                        <i class="fas fa-cube"></i>
                                        <?= $category['products_count'] ?>+ Products
                                    </span>
                                    <?php if ($category['featured']): ?>
                                        <span class="featured-badge">
                                            <i class="fas fa-star"></i>
                                            Popular
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="category-arrow">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                        </div>

                        <div class="category-hover-effect"></div>
                    </a>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="categories-footer" data-aos="fade-up" data-aos-delay="800">
            <p class="footer-text">Can't find what you're looking for?</p>
            <a href="<?= UrlHelper::url('/products') ?>" class="view-all-btn">
                <span>Browse All Products</span>
                <i class="fas fa-arrow-right"></i>
            </a>
        </div>
    </div>
</section>

<!-- Visual Separator -->
<div class="section-separator-thick"></div>

<!-- Enhanced Featured Products Section -->
<section class="featured-section section-enhanced" id="featuredSection">
    <div class="container-enhanced">
        <div class="section-header-enhanced" data-aos="fade-up">
            <div class="section-badge">
                <i class="fas fa-star"></i>
                <span>Featured Products</span>
            </div>
            <h2 class="section-title">Premium Laboratory Equipment</h2>
            <p class="section-subtitle">
                Discover our most popular and highly-rated products,
                <span class="highlight-text">trusted by professionals worldwide</span> for their exceptional quality and performance.
            </p>

            <!-- Product Filter Tabs -->
            <div class="product-filter-tabs">
                <button class="filter-tab active" data-filter="all">
                    <i class="fas fa-th"></i>
                    <span>All Products</span>
                </button>
                <button class="filter-tab" data-filter="equipment">
                    <i class="fas fa-microscope"></i>
                    <span>Equipment</span>
                </button>
                <button class="filter-tab" data-filter="chemicals">
                    <i class="fas fa-flask"></i>
                    <span>Chemicals</span>
                </button>
                <button class="filter-tab" data-filter="glassware">
                    <i class="fas fa-vial"></i>
                    <span>Glassware</span>
                </button>
            </div>
        </div>

        <div class="featured-products-container" data-aos="fade-up" data-aos-delay="200">
            <div class="products-grid enhanced-grid">
                <?php
                $sampleProducts = [
                    [
                        'id' => 1,
                        'name' => 'Digital Microscope Pro 4000X',
                        'slug' => 'digital-microscope-pro-4000x',
                        'price' => 2499.99,
                        'sale_price' => 1999.99,
                        'category' => 'equipment',
                        'category_name' => 'Laboratory Equipment',
                        'rating' => 4.9,
                        'reviews_count' => 127,
                        'image' => 'microscope.jpg',
                        'is_featured' => true,
                        'is_bestseller' => true,
                        'stock_status' => 'in_stock',
                        'features' => ['4000X Magnification', 'Digital Display', 'USB Connectivity']
                    ],
                    [
                        'id' => 2,
                        'name' => 'Premium Chemical Reagent Set',
                        'slug' => 'premium-chemical-reagent-set',
                        'price' => 299.99,
                        'sale_price' => null,
                        'category' => 'chemicals',
                        'category_name' => 'Chemicals & Reagents',
                        'rating' => 4.8,
                        'reviews_count' => 89,
                        'image' => 'reagent-set.jpg',
                        'is_featured' => true,
                        'is_bestseller' => false,
                        'stock_status' => 'in_stock',
                        'features' => ['High Purity', 'Complete Set', 'Safety Certified']
                    ],
                    [
                        'id' => 3,
                        'name' => 'Borosilicate Glass Beaker Set',
                        'slug' => 'borosilicate-glass-beaker-set',
                        'price' => 149.99,
                        'sale_price' => 119.99,
                        'category' => 'glassware',
                        'category_name' => 'Glassware',
                        'rating' => 4.7,
                        'reviews_count' => 156,
                        'image' => 'beaker-set.jpg',
                        'is_featured' => true,
                        'is_bestseller' => true,
                        'stock_status' => 'low_stock',
                        'features' => ['Heat Resistant', '6-Piece Set', 'Graduated Markings']
                    ],
                    [
                        'id' => 4,
                        'name' => 'Precision Analytical Balance',
                        'slug' => 'precision-analytical-balance',
                        'price' => 1899.99,
                        'sale_price' => null,
                        'category' => 'equipment',
                        'category_name' => 'Laboratory Equipment',
                        'rating' => 4.9,
                        'reviews_count' => 73,
                        'image' => 'balance.jpg',
                        'is_featured' => true,
                        'is_bestseller' => false,
                        'stock_status' => 'in_stock',
                        'features' => ['0.1mg Precision', 'Internal Calibration', 'Data Logging']
                    ],
                    [
                        'id' => 5,
                        'name' => 'Laboratory Safety Kit Complete',
                        'slug' => 'laboratory-safety-kit-complete',
                        'price' => 199.99,
                        'sale_price' => 159.99,
                        'category' => 'safety',
                        'category_name' => 'Safety Equipment',
                        'rating' => 4.6,
                        'reviews_count' => 94,
                        'image' => 'safety-kit.jpg',
                        'is_featured' => true,
                        'is_bestseller' => false,
                        'stock_status' => 'in_stock',
                        'features' => ['Complete Protection', 'OSHA Compliant', 'Durable Materials']
                    ],
                    [
                        'id' => 6,
                        'name' => 'pH Meter Digital Professional',
                        'slug' => 'ph-meter-digital-professional',
                        'price' => 399.99,
                        'sale_price' => null,
                        'category' => 'equipment',
                        'category_name' => 'Measurement Tools',
                        'rating' => 4.8,
                        'reviews_count' => 112,
                        'image' => 'ph-meter.jpg',
                        'is_featured' => true,
                        'is_bestseller' => true,
                        'stock_status' => 'in_stock',
                        'features' => ['±0.01 pH Accuracy', 'Auto Calibration', 'Waterproof']
                    ]
                ];

                foreach ($sampleProducts as $index => $product): ?>
                    <div class="enhanced-product-card"
                        data-aos="zoom-in"
                        data-aos-delay="<?= 300 + ($index * 100) ?>"
                        data-category="<?= $product['category'] ?>"
                        data-product-id="<?= $product['id'] ?>">

                        <div class="product-image-container">
                            <div class="product-image">
                                <img src="<?= UrlHelper::url('/uploads/products/' . $product['image']) ?>"
                                    alt="<?= htmlspecialchars($product['name']) ?>"
                                    loading="lazy"
                                    class="main-image">
                                <div class="image-overlay"></div>
                            </div>

                            <!-- Product Badges -->
                            <div class="product-badges">
                                <?php if ($product['sale_price']): ?>
                                    <div class="product-badge sale-badge">
                                        <span>-<?= round((($product['price'] - $product['sale_price']) / $product['price']) * 100) ?>%</span>
                                    </div>
                                <?php endif; ?>
                                <?php if ($product['is_bestseller']): ?>
                                    <div class="product-badge bestseller-badge">
                                        <i class="fas fa-fire"></i>
                                        <span>Bestseller</span>
                                    </div>
                                <?php endif; ?>
                                <?php if ($product['stock_status'] === 'low_stock'): ?>
                                    <div class="product-badge stock-badge">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <span>Low Stock</span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Quick Actions -->
                            <div class="product-quick-actions">
                                <button class="quick-action-btn quick-view"
                                    data-product-id="<?= $product['id'] ?>"
                                    title="Quick View">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="quick-action-btn add-to-wishlist"
                                    data-product-id="<?= $product['id'] ?>"
                                    title="Add to Wishlist">
                                    <i class="far fa-heart"></i>
                                </button>
                                <button class="quick-action-btn add-to-compare"
                                    data-product-id="<?= $product['id'] ?>"
                                    title="Compare">
                                    <i class="fas fa-balance-scale"></i>
                                </button>
                            </div>
                        </div>

                        <div class="product-content">
                            <div class="product-header">
                                <div class="product-category">
                                    <i class="fas fa-tag"></i>
                                    <span><?= htmlspecialchars($product['category_name']) ?></span>
                                </div>
                                <div class="product-rating">
                                    <div class="stars">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <i class="fas fa-star <?= $i <= floor($product['rating']) ? 'active' : '' ?>"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <span class="rating-text"><?= $product['rating'] ?> (<?= $product['reviews_count'] ?>)</span>
                                </div>
                            </div>

                            <h3 class="product-title">
                                <a href="<?= UrlHelper::url('/product/' . $product['slug']) ?>">
                                    <?= htmlspecialchars($product['name']) ?>
                                </a>
                            </h3>

                            <div class="product-features">
                                <?php foreach (array_slice($product['features'], 0, 3) as $feature): ?>
                                    <span class="feature-tag">
                                        <i class="fas fa-check"></i>
                                        <?= htmlspecialchars($feature) ?>
                                    </span>
                                <?php endforeach; ?>
                            </div>


                            <div class="product-pricing">
                                <div class="price-container">
                                    <span class="current-price">
                                        $<?= number_format($product['sale_price'] ?: $product['price'], 2) ?>
                                    </span>
                                    <?php if ($product['sale_price']): ?>
                                        <span class="original-price">
                                            $<?= number_format($product['price'], 2) ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                                <?php if ($product['sale_price']): ?>
                                    <div class="savings-info">
                                        <span class="savings-text">
                                            Save $<?= number_format($product['price'] - $product['sale_price'], 2) ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="product-actions">
                                <button class="btn-add-to-cart"
                                    data-product-id="<?= $product['id'] ?>"
                                    data-quantity="1">
                                    <div class="btn-content">
                                        <i class="fas fa-shopping-cart"></i>
                                        <span>Add to Cart</span>
                                    </div>
                                    <div class="btn-loading">
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </div>
                                </button>

                                <div class="secondary-actions">
                                    <button class="btn-secondary buy-now"
                                        data-product-id="<?= $product['id'] ?>">
                                        <i class="fas fa-bolt"></i>
                                        <span>Buy Now</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Enhanced Section Footer -->
        <div class="section-footer" data-aos="fade-up" data-aos-delay="1000">
            <div class="footer-content">
                <div class="footer-text">
                    <h3>Need Help Choosing?</h3>
                    <p>Our laboratory experts are here to help you find the perfect equipment for your needs.</p>
                </div>
                <div class="footer-actions">
                    <a href="<?= UrlHelper::url('/products') ?>" class="btn-view-all">
                        <div class="btn-content">
                            <i class="fas fa-th-large"></i>
                            <span>View All Products</span>
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </a>
                    <a href="<?= UrlHelper::url('/contact') ?>" class="btn-expert-help">
                        <div class="btn-content">
                            <i class="fas fa-user-md"></i>
                            <span>Get Expert Help</span>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Services & Features Section -->
<section class="services-section" id="servicesSection">
    <div class="container">
        <div class="section-header" data-aos="fade-up">
            <div class="section-badge">
                <i class="fas fa-cogs"></i>
                <span>Our Services</span>
            </div>
            <h2 class="section-title">Why Choose Cleanance Lab?</h2>
            <p class="section-subtitle">
                Experience the difference with our comprehensive laboratory solutions and
                <span class="highlight-text">unmatched customer service</span> that sets us apart from the competition.
            </p>
        </div>

        <div class="services-grid" data-aos="fade-up" data-aos-delay="200">
            <!-- Service 1: Quality Assurance -->
            <div class="service-card" data-aos="zoom-in" data-aos-delay="300">
                <div class="service-icon">
                    <i class="fas fa-certificate"></i>
                    <div class="icon-background"></div>
                </div>
                <div class="service-content">
                    <h3 class="service-title">ISO 9001 Certified Quality</h3>
                    <p class="service-description">
                        Every product undergoes rigorous quality testing to ensure it meets the highest international standards for laboratory use.
                    </p>
                    <ul class="service-features">
                        <li><i class="fas fa-check"></i> Rigorous quality control</li>
                        <li><i class="fas fa-check"></i> International certifications</li>
                        <li><i class="fas fa-check"></i> Batch testing reports</li>
                    </ul>
                </div>
            </div>

            <!-- Service 2: Fast Delivery -->
            <div class="service-card" data-aos="zoom-in" data-aos-delay="400">
                <div class="service-icon">
                    <i class="fas fa-shipping-fast"></i>
                    <div class="icon-background"></div>
                </div>
                <div class="service-content">
                    <h3 class="service-title">Express Delivery Worldwide</h3>
                    <p class="service-description">
                        Get your laboratory supplies delivered quickly and safely with our express shipping options available globally.
                    </p>
                    <ul class="service-features">
                        <li><i class="fas fa-check"></i> 24-48h express delivery</li>
                        <li><i class="fas fa-check"></i> Temperature-controlled shipping</li>
                        <li><i class="fas fa-check"></i> Real-time tracking</li>
                    </ul>
                </div>
            </div>

            <!-- Service 3: Expert Support -->
            <div class="service-card" data-aos="zoom-in" data-aos-delay="500">
                <div class="service-icon">
                    <i class="fas fa-user-md"></i>
                    <div class="icon-background"></div>
                </div>
                <div class="service-content">
                    <h3 class="service-title">Expert Technical Support</h3>
                    <p class="service-description">
                        Our team of laboratory professionals provides comprehensive technical support and product guidance.
                    </p>
                    <ul class="service-features">
                        <li><i class="fas fa-check"></i> 24/7 technical support</li>
                        <li><i class="fas fa-check"></i> Product consultation</li>
                        <li><i class="fas fa-check"></i> Installation assistance</li>
                    </ul>
                </div>
            </div>

            <!-- Service 4: Custom Solutions -->
            <div class="service-card" data-aos="zoom-in" data-aos-delay="600">
                <div class="service-icon">
                    <i class="fas fa-puzzle-piece"></i>
                    <div class="icon-background"></div>
                </div>
                <div class="service-content">
                    <h3 class="service-title">Custom Laboratory Solutions</h3>
                    <p class="service-description">
                        Tailored solutions designed specifically for your laboratory's unique requirements and research needs.
                    </p>
                    <ul class="service-features">
                        <li><i class="fas fa-check"></i> Custom equipment design</li>
                        <li><i class="fas fa-check"></i> Bulk order discounts</li>
                        <li><i class="fas fa-check"></i> Specialized configurations</li>
                    </ul>
                </div>
            </div>

            <!-- Service 5: Warranty & Support -->
            <div class="service-card" data-aos="zoom-in" data-aos-delay="700">
                <div class="service-icon">
                    <i class="fas fa-shield-alt"></i>
                    <div class="icon-background"></div>
                </div>
                <div class="service-content">
                    <h3 class="service-title">Comprehensive Warranty</h3>
                    <p class="service-description">
                        Enjoy peace of mind with our extensive warranty coverage and dedicated after-sales support services.
                    </p>
                    <ul class="service-features">
                        <li><i class="fas fa-check"></i> Extended warranty options</li>
                        <li><i class="fas fa-check"></i> Free maintenance support</li>
                        <li><i class="fas fa-check"></i> Replacement guarantee</li>
                    </ul>
                </div>
            </div>

            <!-- Service 6: Training & Education -->
            <div class="service-card" data-aos="zoom-in" data-aos-delay="800">
                <div class="service-icon">
                    <i class="fas fa-graduation-cap"></i>
                    <div class="icon-background"></div>
                </div>
                <div class="service-content">
                    <h3 class="service-title">Training & Education</h3>
                    <p class="service-description">
                        Comprehensive training programs and educational resources to maximize your laboratory's potential.
                    </p>
                    <ul class="service-features">
                        <li><i class="fas fa-check"></i> Equipment training sessions</li>
                        <li><i class="fas fa-check"></i> Safety protocols education</li>
                        <li><i class="fas fa-check"></i> Online learning resources</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Service Statistics -->
        <div class="service-stats" data-aos="fade-up" data-aos-delay="900">
            <div class="stats-container">
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <span class="stat-number">24/7</span>
                        <span class="stat-label">Support Available</span>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="stat-content">
                        <span class="stat-number">50+</span>
                        <span class="stat-label">Countries Served</span>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-award"></i>
                    </div>
                    <div class="stat-content">
                        <span class="stat-number">99.8%</span>
                        <span class="stat-label">Customer Satisfaction</span>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-shipping-fast"></i>
                    </div>
                    <div class="stat-content">
                        <span class="stat-number">48h</span>
                        <span class="stat-label">Average Delivery</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Visual Separator -->
<div class="section-separator-glow"></div>

<!-- About/Company Section -->
<section class="about-section section-enhanced" id="aboutSection">
    <div class="container-enhanced">
        <div class="about-content">
            <div class="about-text" data-aos="fade-right">
                <div class="section-badge">
                    <i class="fas fa-building"></i>
                    <span>About Cleanance Lab</span>
                </div>
                <h2 class="section-title">Leading Laboratory Solutions Provider</h2>
                <p class="section-subtitle">
                    For over <span class="highlight-text">15 years</span>, Cleanance Lab has been at the forefront of
                    laboratory innovation, providing cutting-edge equipment and supplies to research institutions,
                    universities, and laboratories worldwide.
                </p>

                <div class="about-highlights">
                    <div class="highlight-item">
                        <div class="highlight-icon">
                            <i class="fas fa-award"></i>
                        </div>
                        <div class="highlight-content">
                            <h4>Industry Recognition</h4>
                            <p>Winner of the Laboratory Excellence Award 2023 for outstanding product quality and innovation.</p>
                        </div>
                    </div>

                    <div class="highlight-item">
                        <div class="highlight-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="highlight-content">
                            <h4>Trusted by Professionals</h4>
                            <p>Over 15,000 satisfied customers across 50+ countries trust our laboratory solutions.</p>
                        </div>
                    </div>

                    <div class="highlight-item">
                        <div class="highlight-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <div class="highlight-content">
                            <h4>Sustainable Practices</h4>
                            <p>Committed to environmental responsibility with eco-friendly packaging and sustainable sourcing.</p>
                        </div>
                    </div>
                </div>

                <div class="about-actions">
                    <a href="<?= UrlHelper::url('/about') ?>" class="btn-learn-more">
                        <span>Learn More About Us</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                    <a href="<?= UrlHelper::url('/contact') ?>" class="btn-contact">
                        <i class="fas fa-phone"></i>
                        <span>Contact Our Team</span>
                    </a>
                </div>
            </div>

            <div class="about-visual" data-aos="fade-left">
                <div class="visual-container">
                    <div class="main-image">
                        <img src="<?= UrlHelper::url('/assets/img/lab-team.jpg') ?>"
                            alt="Cleanance Lab Team"
                            loading="lazy">
                        <div class="image-overlay"></div>
                    </div>

                    <div class="floating-stats">
                        <div class="stat-card">
                            <div class="stat-number">15+</div>
                            <div class="stat-label">Years Experience</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">850+</div>
                            <div class="stat-label">Products</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">50+</div>
                            <div class="stat-label">Countries</div>
                        </div>
                    </div>

                    <div class="certification-badges">
                        <div class="cert-badge">
                            <i class="fas fa-certificate"></i>
                            <span>ISO 9001</span>
                        </div>
                        <div class="cert-badge">
                            <i class="fas fa-shield-alt"></i>
                            <span>FDA Approved</span>
                        </div>
                        <div class="cert-badge">
                            <i class="fas fa-leaf"></i>
                            <span>Eco Certified</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Visual Separator -->
<div class="section-separator-thick"></div>

<!-- Enhanced Testimonials Section -->
<section class="testimonials-section section-enhanced" id="testimonialsSection">
    <div class="container-enhanced">
        <div class="section-header-enhanced" data-aos="fade-up">
            <div class="section-badge">
                <i class="fas fa-quote-left"></i>
                <span>Customer Testimonials</span>
            </div>
            <h2 class="section-title">What Our Customers Say</h2>
            <p class="section-subtitle">
                Don't just take our word for it - hear from the <span class="highlight-text">professionals who trust us</span>
                with their most critical laboratory needs.
            </p>
        </div>

        <div class="testimonials-container" data-aos="fade-up" data-aos-delay="200">
            <div class="testimonials-grid">
                <!-- Testimonial 1 -->
                <div class="testimonial-card" data-aos="zoom-in" data-aos-delay="300">
                    <div class="testimonial-header">
                        <div class="customer-avatar">
                            <img src="<?= UrlHelper::url('/assets/img/testimonials/customer1.jpg') ?>"
                                alt="Dr. Sarah Johnson" loading="lazy">
                        </div>
                        <div class="customer-info">
                            <h4 class="customer-name">Dr. Sarah Johnson</h4>
                            <p class="customer-title">Research Director</p>
                            <p class="customer-company">Harvard Medical School</p>
                        </div>
                        <div class="testimonial-rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <div class="testimonial-content">
                        <div class="quote-icon">
                            <i class="fas fa-quote-left"></i>
                        </div>
                        <p class="testimonial-text">
                            "Cleanance Lab has been our trusted partner for over 5 years. Their equipment quality is exceptional,
                            and their technical support team is incredibly knowledgeable. The precision microscope we purchased
                            has been instrumental in our breakthrough research."
                        </p>
                        <div class="testimonial-meta">
                            <span class="verified-badge">
                                <i class="fas fa-check-circle"></i>
                                Verified Purchase
                            </span>
                            <span class="purchase-date">March 2024</span>
                        </div>
                    </div>
                </div>

                <!-- Testimonial 2 -->
                <div class="testimonial-card" data-aos="zoom-in" data-aos-delay="400">
                    <div class="testimonial-header">
                        <div class="customer-avatar">
                            <img src="<?= UrlHelper::url('/assets/img/testimonials/customer2.jpg') ?>"
                                alt="Prof. Michael Chen" loading="lazy">
                        </div>
                        <div class="customer-info">
                            <h4 class="customer-name">Prof. Michael Chen</h4>
                            <p class="customer-title">Laboratory Manager</p>
                            <p class="customer-company">MIT Chemistry Dept.</p>
                        </div>
                        <div class="testimonial-rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <div class="testimonial-content">
                        <div class="quote-icon">
                            <i class="fas fa-quote-left"></i>
                        </div>
                        <p class="testimonial-text">
                            "Outstanding service and product quality! The chemical reagents are always pure and properly packaged.
                            Their express delivery saved us during a critical experiment deadline. Highly recommend to any serious laboratory."
                        </p>
                        <div class="testimonial-meta">
                            <span class="verified-badge">
                                <i class="fas fa-check-circle"></i>
                                Verified Purchase
                            </span>
                            <span class="purchase-date">February 2024</span>
                        </div>
                    </div>
                </div>

                <!-- Testimonial 3 -->
                <div class="testimonial-card" data-aos="zoom-in" data-aos-delay="500">
                    <div class="testimonial-header">
                        <div class="customer-avatar">
                            <img src="<?= UrlHelper::url('/assets/img/testimonials/customer3.jpg') ?>"
                                alt="Dr. Emily Rodriguez" loading="lazy">
                        </div>
                        <div class="customer-info">
                            <h4 class="customer-name">Dr. Emily Rodriguez</h4>
                            <p class="customer-title">Quality Control Manager</p>
                            <p class="customer-company">Pfizer Pharmaceuticals</p>
                        </div>
                        <div class="testimonial-rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <div class="testimonial-content">
                        <div class="quote-icon">
                            <i class="fas fa-quote-left"></i>
                        </div>
                        <p class="testimonial-text">
                            "The analytical balance we ordered exceeded our expectations. Precision is crucial in pharmaceutical testing,
                            and this equipment delivers consistent, reliable results. The calibration service was also top-notch."
                        </p>
                        <div class="testimonial-meta">
                            <span class="verified-badge">
                                <i class="fas fa-check-circle"></i>
                                Verified Purchase
                            </span>
                            <span class="purchase-date">January 2024</span>
                        </div>
                    </div>
                </div>

                <!-- Testimonial 4 -->
                <div class="testimonial-card" data-aos="zoom-in" data-aos-delay="600">
                    <div class="testimonial-header">
                        <div class="customer-avatar">
                            <img src="<?= UrlHelper::url('/assets/img/testimonials/customer4.jpg') ?>"
                                alt="Dr. James Wilson" loading="lazy">
                        </div>
                        <div class="customer-info">
                            <h4 class="customer-name">Dr. James Wilson</h4>
                            <p class="customer-title">Lab Coordinator</p>
                            <p class="customer-company">Stanford University</p>
                        </div>
                        <div class="testimonial-rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <div class="testimonial-content">
                        <div class="quote-icon">
                            <i class="fas fa-quote-left"></i>
                        </div>
                        <p class="testimonial-text">
                            "Cleanance Lab's customer service is unmatched. When we had questions about equipment setup,
                            their technical team provided detailed guidance and even offered remote assistance. True professionals!"
                        </p>
                        <div class="testimonial-meta">
                            <span class="verified-badge">
                                <i class="fas fa-check-circle"></i>
                                Verified Purchase
                            </span>
                            <span class="purchase-date">December 2023</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Testimonials Summary -->
        <div class="testimonials-summary" data-aos="fade-up" data-aos-delay="700">
            <div class="summary-stats">
                <div class="summary-stat">
                    <div class="stat-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-content">
                        <span class="stat-number">4.9/5</span>
                        <span class="stat-label">Average Rating</span>
                    </div>
                </div>
                <div class="summary-stat">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <span class="stat-number">2,847</span>
                        <span class="stat-label">Happy Customers</span>
                    </div>
                </div>
                <div class="summary-stat">
                    <div class="stat-icon">
                        <i class="fas fa-thumbs-up"></i>
                    </div>
                    <div class="stat-content">
                        <span class="stat-number">98%</span>
                        <span class="stat-label">Would Recommend</span>
                    </div>
                </div>
            </div>

            <div class="testimonials-cta">
                <p>Join thousands of satisfied customers</p>
                <a href="<?= UrlHelper::url('/products') ?>" class="btn-testimonials-cta">
                    <span>Shop Now</span>
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Visual Separator -->
<div class="section-separator-glow"></div>

<!-- Trust Indicators Section -->
<section class="trust-section section-enhanced" id="trustSection">
    <div class="container-enhanced">
        <div class="section-header-enhanced" data-aos="fade-up">
            <div class="section-badge">
                <i class="fas fa-shield-check"></i>
                <span>Trust & Security</span>
            </div>
            <h2 class="section-title">Your Trust is Our Priority</h2>
            <p class="section-subtitle">
                We maintain the highest standards of <span class="highlight-text">quality, security, and reliability</span>
                to ensure your complete confidence in our products and services.
            </p>
        </div>

        <div class="trust-indicators" data-aos="fade-up" data-aos-delay="200">
            <div class="trust-grid">
                <!-- Certification 1 -->
                <div class="trust-card" data-aos="zoom-in" data-aos-delay="300">
                    <div class="trust-icon">
                        <i class="fas fa-certificate"></i>
                    </div>
                    <div class="trust-content">
                        <h3 class="trust-title">ISO 9001:2015 Certified</h3>
                        <p class="trust-description">
                            International quality management system certification ensuring consistent product quality.
                        </p>
                        <div class="trust-badge">
                            <span>Certified Since 2018</span>
                        </div>
                    </div>
                </div>

                <!-- Security 2 -->
                <div class="trust-card" data-aos="zoom-in" data-aos-delay="400">
                    <div class="trust-icon">
                        <i class="fas fa-lock"></i>
                    </div>
                    <div class="trust-content">
                        <h3 class="trust-title">SSL Encrypted Transactions</h3>
                        <p class="trust-description">
                            256-bit SSL encryption protects all your personal and payment information during transactions.
                        </p>
                        <div class="trust-badge">
                            <span>Bank-Level Security</span>
                        </div>
                    </div>
                </div>

                <!-- Guarantee 3 -->
                <div class="trust-card" data-aos="zoom-in" data-aos-delay="500">
                    <div class="trust-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <div class="trust-content">
                        <h3 class="trust-title">100% Satisfaction Guarantee</h3>
                        <p class="trust-description">
                            Not satisfied with your purchase? We offer a full refund within 30 days, no questions asked.
                        </p>
                        <div class="trust-badge">
                            <span>30-Day Money Back</span>
                        </div>
                    </div>
                </div>

                <!-- Compliance 4 -->
                <div class="trust-card" data-aos="zoom-in" data-aos-delay="600">
                    <div class="trust-icon">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                    <div class="trust-content">
                        <h3 class="trust-title">FDA Compliant Products</h3>
                        <p class="trust-description">
                            All our products meet or exceed FDA regulations and safety standards for laboratory use.
                        </p>
                        <div class="trust-badge">
                            <span>FDA Approved</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Badges -->
        <div class="security-badges" data-aos="fade-up" data-aos-delay="700">
            <div class="badges-container">
                <div class="security-badge">
                    <img src="<?= UrlHelper::url('/assets/img/badges/ssl-badge.png') ?>"
                        alt="SSL Secured" loading="lazy">
                </div>
                <div class="security-badge">
                    <img src="<?= UrlHelper::url('/assets/img/badges/iso-badge.png') ?>"
                        alt="ISO 9001 Certified" loading="lazy">
                </div>
                <div class="security-badge">
                    <img src="<?= UrlHelper::url('/assets/img/badges/fda-badge.png') ?>"
                        alt="FDA Compliant" loading="lazy">
                </div>
                <div class="security-badge">
                    <img src="<?= UrlHelper::url('/assets/img/badges/secure-badge.png') ?>"
                        alt="Secure Payments" loading="lazy">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Visual Separator -->
<div class="section-separator-thick"></div>

<!-- Newsletter/CTA Section -->
<section class="newsletter-section section-enhanced" id="newsletterSection">
    <div class="newsletter-background">
        <div class="newsletter-pattern"></div>
        <div class="newsletter-glow"></div>
    </div>

    <div class="container-enhanced">
        <div class="newsletter-content" data-aos="fade-up">
            <div class="newsletter-text">
                <div class="section-badge">
                    <i class="fas fa-envelope"></i>
                    <span>Stay Updated</span>
                </div>
                <h2 class="section-title">Get the Latest Laboratory Insights</h2>
                <p class="section-subtitle">
                    Subscribe to our newsletter for <span class="highlight-text">exclusive offers, product updates,</span>
                    and expert laboratory tips delivered directly to your inbox.
                </p>

                <div class="newsletter-benefits">
                    <div class="benefit-item">
                        <i class="fas fa-percent"></i>
                        <span>Exclusive discounts and early access to sales</span>
                    </div>
                    <div class="benefit-item">
                        <i class="fas fa-flask"></i>
                        <span>Latest product launches and innovations</span>
                    </div>
                    <div class="benefit-item">
                        <i class="fas fa-graduation-cap"></i>
                        <span>Expert tips and laboratory best practices</span>
                    </div>
                </div>
            </div>

            <div class="newsletter-form-container" data-aos="fade-up" data-aos-delay="200">
                <form class="newsletter-form" id="newsletterForm">
                    <div class="form-group">
                        <div class="input-wrapper">
                            <input type="email"
                                id="newsletterEmail"
                                name="email"
                                placeholder="Enter your email address"
                                required
                                class="newsletter-input">
                            <div class="input-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                        </div>
                        <button type="submit" class="newsletter-btn">
                            <span class="btn-text">Subscribe Now</span>
                            <span class="btn-loading">
                                <i class="fas fa-spinner fa-spin"></i>
                            </span>
                            <i class="fas fa-arrow-right btn-arrow"></i>
                        </button>
                    </div>

                    <div class="form-footer">
                        <div class="privacy-notice">
                            <i class="fas fa-lock"></i>
                            <span>We respect your privacy. Unsubscribe at any time.</span>
                        </div>
                        <div class="subscriber-count">
                            <i class="fas fa-users"></i>
                            <span>Join 12,000+ subscribers</span>
                        </div>
                    </div>
                </form>

                <div class="newsletter-success" id="newsletterSuccess" style="display: none;">
                    <div class="success-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3>Thank You for Subscribing!</h3>
                    <p>You'll receive our latest updates and exclusive offers soon.</p>
                </div>
            </div>
        </div>
    </div>
</section>



<style>
    /* Enhanced Spacing System for Better Visual Separation */
    :root {
        /* Extended Spacing Scale */
        --spacing-4xl: 5rem;
        /* 80px */
        --spacing-5xl: 6rem;
        /* 96px */
        --spacing-6xl: 8rem;
        /* 128px */
        --spacing-7xl: 10rem;
        /* 160px */
        --spacing-8xl: 12rem;
        /* 192px */

        /* Section-specific spacing */
        --section-padding-mobile: var(--spacing-2xl);
        --section-padding-tablet: var(--spacing-3xl);
        --section-padding-desktop: var(--spacing-4xl);
        --section-gap: var(--spacing-xl);
        --content-max-width: 1400px;

        /* Visual separator variables */
        --separator-height: 1px;
        --separator-color: rgba(126, 87, 194, 0.1);
        --separator-gradient: linear-gradient(90deg, transparent 0%, var(--separator-color) 50%, transparent 100%);
    }

    /* Section Separator Utility Classes */
    .section-separator {
        height: var(--separator-height);
        background: var(--separator-gradient);
        margin: var(--spacing-2xl) 0;
        opacity: 0.6;
        transition: opacity 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .section-separator::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(126, 87, 194, 0.3), transparent);
        animation: shimmer 3s ease-in-out infinite;
    }

    .section-separator-thick {
        height: 2px;
        background: var(--separator-gradient);
        margin: var(--spacing-3xl) 0;
        opacity: 0.8;
        transition: opacity 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .section-separator-thick::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(126, 87, 194, 0.4), transparent);
        animation: shimmer 4s ease-in-out infinite;
    }

    .section-separator-glow {
        height: var(--separator-height);
        background: var(--separator-gradient);
        margin: var(--spacing-3xl) 0;
        box-shadow: 0 0 20px rgba(126, 87, 194, 0.2);
        opacity: 0.7;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .section-separator-glow::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(126, 87, 194, 0.5), transparent);
        animation: shimmer 5s ease-in-out infinite;
    }

    @keyframes shimmer {
        0% {
            left: -100%;
        }

        50% {
            left: 100%;
        }

        100% {
            left: 100%;
        }
    }

    /* Enhanced Section Base Styles */
    .section-enhanced {
        position: relative;
        padding: var(--section-padding-desktop) 0;
        transition: all 0.3s ease;
        background-size: 100px 100px;
        background-image:
            radial-gradient(circle at 25% 25%, rgba(126, 87, 194, 0.02) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(126, 87, 194, 0.02) 0%, transparent 50%);
    }

    .section-enhanced::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: var(--separator-height);
        background: var(--separator-gradient);
        opacity: 0.5;
    }

    .section-enhanced::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: var(--separator-height);
        background: var(--separator-gradient);
        opacity: 0.5;
    }

    /* Container with enhanced spacing */
    .container-enhanced {
        max-width: var(--content-max-width);
        margin: 0 auto;
        padding: 0 var(--spacing-lg);
    }

    /* Section header with improved spacing */
    .section-header-enhanced {
        text-align: center;
        margin-bottom: var(--spacing-4xl);
        padding: 0 var(--spacing-lg);
    }

    .section-header-enhanced .section-badge {
        margin-bottom: var(--spacing-xl);
    }

    .section-header-enhanced .section-title {
        margin-bottom: var(--spacing-lg);
    }

    .section-header-enhanced .section-subtitle {
        margin-bottom: var(--spacing-xl);
    }

    /* Enhanced Content Spacing */
    .section-content {
        margin-bottom: var(--spacing-3xl);
    }

    .section-footer {
        margin-top: var(--spacing-4xl);
        padding-top: var(--spacing-xl);
        border-top: 1px solid var(--separator-color);
    }

    /* Improved Grid Spacing */
    .enhanced-grid {
        gap: var(--spacing-xl);
        margin-top: var(--spacing-3xl);
        margin-bottom: var(--spacing-2xl);
    }

    .categories-grid,
    .services-grid,
    .testimonials-grid,
    .trust-grid {
        gap: var(--spacing-xl);
        margin-top: var(--spacing-3xl);
        margin-bottom: var(--spacing-2xl);
    }

    /* Enhanced CTA and Button Spacing */
    .cta-section {
        margin: var(--spacing-4xl) 0;
        padding: var(--spacing-2xl) 0;
        text-align: center;
    }

    .btn-group {
        display: flex;
        gap: var(--spacing-lg);
        justify-content: center;
        flex-wrap: wrap;
        margin-top: var(--spacing-xl);
    }

    .section-actions {
        margin-top: var(--spacing-3xl);
        text-align: center;
    }

    /* Enhanced Card Spacing */
    .card-enhanced {
        margin-bottom: var(--spacing-xl);
        padding: var(--spacing-xl);
    }

    .card-header {
        margin-bottom: var(--spacing-lg);
    }

    .card-content {
        margin-bottom: var(--spacing-lg);
    }

    .card-footer {
        margin-top: var(--spacing-lg);
        padding-top: var(--spacing-lg);
        border-top: 1px solid var(--separator-color);
    }

    /* Enhanced Hero Section */
    .hero {
        min-height: 100vh;
        position: relative;
        display: flex;
        align-items: center;
        overflow: hidden;
        background: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-card) 50%, var(--dark-surface) 100%);
        padding: 0 0 var(--spacing-xl) 0;
        margin-bottom: 0;
    }

    .hero-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 0;
    }

    .hero-particles {
        position: absolute;
        width: 100%;
        height: 100%;
        background-image:
            radial-gradient(2px 2px at 20px 30px, rgba(126, 87, 194, 0.3), transparent),
            radial-gradient(2px 2px at 40px 70px, rgba(126, 87, 194, 0.2), transparent),
            radial-gradient(1px 1px at 90px 40px, rgba(126, 87, 194, 0.4), transparent),
            radial-gradient(1px 1px at 130px 80px, rgba(126, 87, 194, 0.3), transparent),
            radial-gradient(2px 2px at 160px 30px, rgba(126, 87, 194, 0.2), transparent);
        background-repeat: repeat;
        background-size: 200px 100px;
        animation: particleFloat 20s linear infinite;
    }

    .hero-gradient-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 20% 30%, rgba(126, 87, 194, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 80% 70%, rgba(126, 87, 194, 0.1) 0%, transparent 50%),
            linear-gradient(135deg, rgba(126, 87, 194, 0.05) 0%, transparent 100%);
    }

    .hero-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-4xl);
        align-items: center;
        position: relative;
        z-index: 1;
        padding: var(--spacing-4xl) 0;
        max-width: var(--content-max-width);
        margin: 0 auto;
    }

    .hero-badge {
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-xs);
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        color: var(--white);
        padding: var(--spacing-sm) var(--spacing-lg);
        border-radius: var(--radius-xl);
        font-size: var(--font-size-sm);
        font-weight: 600;
        margin-bottom: var(--spacing-lg);
        box-shadow: 0 4px 15px rgba(126, 87, 194, 0.3);
        animation: fadeInUp 0.8s ease-out;
    }

    .hero-title {
        margin-bottom: var(--spacing-lg);
        animation: fadeInUp 0.8s ease-out 0.2s both;
    }

    .hero-title-main {
        display: block;
        font-size: var(--font-size-3xl);
        font-weight: 400;
        color: var(--text-secondary);
        margin-bottom: var(--spacing-sm);
        line-height: 1.2;
    }

    .hero-title-accent {
        display: block;
        font-size: 4rem;
        font-weight: 800;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-light));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        line-height: 1.1;
        margin-bottom: var(--spacing-md);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
        color: var(--text-secondary);
        line-height: 1.7;
        margin-bottom: var(--spacing-xl);
        max-width: 90%;
        animation: fadeInUp 0.8s ease-out 0.4s both;
    }

    .hero-stats {
        display: flex;
        gap: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
        animation: fadeInUp 0.8s ease-out 0.6s both;
    }

    .hero-stat {
        text-align: center;
    }

    .hero-stat-number {
        font-size: var(--font-size-2xl);
        font-weight: 800;
        color: var(--primary-purple);
        line-height: 1;
        margin-bottom: var(--spacing-xs);
    }

    .hero-stat-label {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        font-weight: 500;
    }

    .hero-actions {
        display: flex;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
        animation: fadeInUp 0.8s ease-out 0.8s both;
    }

    .hero-cta-primary {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        border: none;
        color: var(--white);
        padding: var(--spacing-lg) var(--spacing-xl);
        border-radius: var(--radius-xl);
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        transition: all var(--transition-fast);
        box-shadow: 0 4px 15px rgba(126, 87, 194, 0.3);
        position: relative;
        overflow: hidden;
    }

    .hero-cta-primary::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .hero-cta-primary:hover::before {
        left: 100%;
    }

    .hero-cta-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(126, 87, 194, 0.4);
        color: var(--white);
    }

    .hero-cta-secondary {
        background: transparent;
        border: 2px solid var(--primary-purple);
        color: var(--primary-purple);
        padding: calc(var(--spacing-lg) - 2px) calc(var(--spacing-xl) - 2px);
        border-radius: var(--radius-xl);
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        transition: all var(--transition-fast);
    }

    .hero-cta-secondary:hover {
        background: var(--primary-purple);
        color: var(--white);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(126, 87, 194, 0.3);
    }

    .hero-features {
        display: flex;
        gap: var(--spacing-lg);
        animation: fadeInUp 0.8s ease-out 1s both;
    }

    .hero-feature {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        font-weight: 500;
    }

    .hero-feature i {
        color: var(--primary-purple);
        font-size: var(--font-size-base);
    }

    .hero-visual-section {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-xl);
    }

    .hero-main-visual {
        position: relative;
        width: 100%;
        height: 500px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .hero-product-showcase {
        position: relative;
        width: 400px;
        height: 400px;
    }

    .hero-product-card {
        position: absolute;
        width: 120px;
        height: 140px;
        background: var(--dark-card);
        border: 2px solid var(--primary-purple);
        border-radius: var(--radius-lg);
        padding: var(--spacing-md);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
        box-shadow: 0 8px 25px rgba(126, 87, 194, 0.2);
        transition: all var(--transition-fast);
        animation: productFloat 6s ease-in-out infinite;
    }

    .hero-product-1 {
        top: 20%;
        left: 10%;
        animation-delay: 0s;
    }

    .hero-product-2 {
        top: 10%;
        right: 15%;
        animation-delay: 2s;
    }

    .hero-product-3 {
        bottom: 20%;
        left: 50%;
        transform: translateX(-50%);
        animation-delay: 4s;
    }

    .hero-product-card:hover {
        transform: translateY(-10px) scale(1.05);
        box-shadow: 0 15px 35px rgba(126, 87, 194, 0.3);
    }

    .hero-product-card .product-image {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: var(--font-size-xl);
    }

    .hero-product-card .product-info {
        text-align: center;
    }

    .hero-product-card .product-name {
        display: block;
        font-size: var(--font-size-xs);
        color: var(--text-primary);
        font-weight: 600;
        margin-bottom: var(--spacing-xs);
    }

    .hero-product-card .product-price {
        display: block;
        font-size: var(--font-size-sm);
        color: var(--primary-purple);
        font-weight: 700;
    }

    .hero-floating-elements {
        position: absolute;
        width: 100%;
        height: 100%;
        pointer-events: none;
    }

    .floating-element {
        position: absolute;
        font-size: var(--font-size-xl);
        color: var(--primary-purple);
        opacity: 0.6;
        animation: elementFloat 8s ease-in-out infinite;
    }

    .element-1 {
        top: 15%;
        left: 5%;
        animation-delay: 0s;
    }

    .element-2 {
        top: 25%;
        right: 5%;
        animation-delay: 1s;
    }

    .element-3 {
        bottom: 30%;
        left: 15%;
        animation-delay: 2s;
    }

    .element-4 {
        top: 60%;
        right: 25%;
        animation-delay: 3s;
    }

    .element-5 {
        bottom: 15%;
        right: 10%;
        animation-delay: 4s;
    }

    .element-6 {
        top: 40%;
        left: 80%;
        animation-delay: 5s;
    }

    .hero-trust-indicators {
        display: flex;
        gap: var(--spacing-lg);
        animation: fadeInUp 0.8s ease-out 1.2s both;
    }

    .trust-indicator {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        background: rgba(126, 87, 194, 0.1);
        padding: var(--spacing-sm) var(--spacing-lg);
        border-radius: var(--radius-lg);
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        font-weight: 500;
        border: 1px solid rgba(126, 87, 194, 0.2);
    }

    .trust-indicator i {
        color: var(--primary-purple);
    }

    .hero-scroll-indicator {
        position: absolute;
        bottom: var(--spacing-xl);
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        animation: fadeInUp 0.8s ease-out 1.4s both;
        cursor: pointer;
        transition: all var(--transition-fast);
    }

    .hero-scroll-indicator:hover {
        color: var(--primary-purple);
        transform: translateX(-50%) translateY(-5px);
    }

    .scroll-arrow {
        width: 40px;
        height: 40px;
        border: 2px solid currentColor;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: bounce 2s infinite;
    }

    /* Hero Animations */
    @keyframes particleFloat {
        0% {
            transform: translateY(0px) translateX(0px);
        }

        33% {
            transform: translateY(-10px) translateX(5px);
        }

        66% {
            transform: translateY(5px) translateX(-5px);
        }

        100% {
            transform: translateY(0px) translateX(0px);
        }
    }

    @keyframes productFloat {

        0%,
        100% {
            transform: translateY(0px) rotate(0deg);
        }

        50% {
            transform: translateY(-15px) rotate(2deg);
        }
    }

    @keyframes elementFloat {

        0%,
        100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.6;
        }

        25% {
            transform: translateY(-10px) rotate(5deg);
            opacity: 0.8;
        }

        50% {
            transform: translateY(-5px) rotate(-3deg);
            opacity: 0.4;
        }

        75% {
            transform: translateY(-12px) rotate(7deg);
            opacity: 0.7;
        }
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes bounce {

        0%,
        20%,
        50%,
        80%,
        100% {
            transform: translateY(0);
        }

        40% {
            transform: translateY(-10px);
        }

        60% {
            transform: translateY(-5px);
        }
    }

    /* Categories Section */
    .categories-section {
        padding: var(--spacing-lg) 0;
        background-color: var(--dark-bg);
    }

    .section-header {
        text-align: center;
        margin-bottom: var(--spacing-2xl);
    }

    .section-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .section-subtitle {
        font-size: var(--font-size-lg);
        color: var(--text-secondary);
    }

    .categories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
    }

    .category-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        text-align: center;
        text-decoration: none;
        color: var(--text-primary);
        transition: all var(--transition-fast);
    }

    .category-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
        border-color: var(--primary-purple);
        color: var(--text-primary);
    }

    .category-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-purple-dark) 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--spacing-md);
        font-size: var(--font-size-2xl);
        color: var(--white);
    }

    .category-name {
        font-size: var(--font-size-xl);
        font-weight: 600;
        margin-bottom: var(--spacing-sm);
    }

    .category-description {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }

    /* Featured & Latest Sections */
    .featured-section,
    .latest-section {
        padding: var(--spacing-xl) 0;
        position: relative;
    }

    .featured-section {
        background-color: var(--dark-card);
    }

    .latest-section {
        background-color: var(--dark-bg);
    }

    .featured-section .container,
    .latest-section .container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 var(--spacing-lg);
    }

    /* Section Spacing */
    .section-header {
        text-align: center;
        margin-bottom: var(--spacing-3xl);
        position: relative;
    }

    .section-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
    }

    .section-title i {
        color: var(--primary-purple);
        font-size: var(--font-size-2xl);
    }

    .section-subtitle {
        font-size: var(--font-size-lg);
        color: var(--text-secondary);
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
    }

    /* Grid Container Improvements */
    .products-grid {
        display: grid;
        gap: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
        align-items: start;
        justify-items: stretch;
        width: 100%;
    }

    .products-grid.grid-cols-5 {
        grid-template-columns: repeat(5, 1fr);
        max-width: 1200px;
        margin: 0 auto var(--spacing-xl);
    }

    .products-grid.grid-cols-4 {
        grid-template-columns: repeat(4, 1fr);
        max-width: 1000px;
        margin: 0 auto var(--spacing-xl);
    }

    /* Ensure all cards have equal height */
    .products-grid .product-card {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    /* Compact Product Cards for Home Page */
    .compact-card {
        height: 100%;
        display: flex;
        flex-direction: column;
        background: var(--dark-card);
        border-radius: var(--radius-lg);
        overflow: hidden;
        transition: all var(--transition-fast);
        box-shadow: var(--shadow-md);
    }

    .compact-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }

    .compact-card .product-image {
        aspect-ratio: 1;
        height: 200px;
        position: relative;
        overflow: hidden;
    }

    .compact-card .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform var(--transition-fast);
    }

    .compact-card:hover .product-image img {
        transform: scale(1.05);
    }

    .compact-card .product-content {
        padding: var(--spacing-md);
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .compact-card .product-title {
        font-size: var(--font-size-sm);
        line-height: 1.3;
        margin-bottom: var(--spacing-xs);
        min-height: 2.6em;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .compact-card .product-category {
        font-size: var(--font-size-xs);
        margin-bottom: var(--spacing-xs);
        min-height: 1.2em;
    }

    .compact-card .product-price {
        margin-bottom: var(--spacing-sm);
        min-height: 1.5em;
    }

    .compact-card .product-price-current {
        font-size: var(--font-size-md);
        font-weight: 600;
    }

    .compact-card .product-price-original {
        font-size: var(--font-size-xs);
    }

    .compact-card .product-rating {
        margin-bottom: var(--spacing-sm);
        min-height: 1.2em;
    }

    .compact-card .product-actions {
        margin-top: auto;
        padding-top: var(--spacing-sm);
    }

    .compact-card .product-actions .btn {
        font-size: var(--font-size-xs);
        padding: var(--spacing-sm) var(--spacing-md);
        width: 100%;
        justify-content: center;
    }

    .product-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        opacity: 0;
        transition: opacity var(--transition-fast);
    }

    .product-card:hover .product-overlay {
        opacity: 1;
    }



    /* Newsletter Section */
    .newsletter-section {
        padding: var(--spacing-lg) 0;
        background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-purple-dark) 100%);
        color: var(--white);
    }

    .newsletter-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-2xl);
        align-items: center;
    }

    .newsletter-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        margin-bottom: var(--spacing-md);
    }

    .newsletter-description {
        font-size: var(--font-size-lg);
        opacity: 0.9;
        line-height: 1.6;
    }

    .newsletter-form {
        display: flex;
        gap: var(--spacing-sm);
    }

    .newsletter-form input {
        flex: 1;
        padding: var(--spacing-md);
        border: none;
        border-radius: var(--radius-md);
        font-size: var(--font-size-base);
        background-color: rgba(255, 255, 255, 0.1);
        color: var(--white);
    }

    .newsletter-form input::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .newsletter-form input:focus {
        outline: none;
        background-color: rgba(255, 255, 255, 0.2);
    }

    .newsletter-form .btn {
        background-color: var(--white);
        color: var(--primary-purple);
        border: none;
    }

    .newsletter-form .btn:hover {
        background-color: rgba(255, 255, 255, 0.9);
    }

    /* Enhanced Responsive Design System */

    /* Large Desktop (1200px+) - Default grid layouts */

    /* Desktop (1024px - 1199px) */
    @media (max-width: 1199px) {
        .container {
            max-width: 1140px;
            padding: 0 var(--spacing-lg);
        }

        .grid-cols-5 {
            grid-template-columns: repeat(4, 1fr);
            gap: var(--spacing-lg);
        }

        .grid-cols-4 {
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-lg);
        }

        .hero-content {
            gap: var(--spacing-2xl);
        }

        .section-header {
            margin-bottom: var(--spacing-xl);
        }
    }

    /* Tablet Landscape (768px - 1023px) */
    @media (max-width: 1023px) {
        .container {
            max-width: 960px;
            padding: 0 var(--spacing-md);
        }

        .grid-cols-5 {
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
        }

        .grid-cols-4 {
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
        }



        .categories-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-lg);
        }

        .products-grid {
            gap: var(--spacing-md);
        }

        /* Adjust section padding */
        .section {
            padding: var(--spacing-2xl) 0;
        }

        .hero {
            min-height: 85vh;
        }

        .hero-content {
            gap: var(--spacing-xl);
        }

        .section-header {
            margin-bottom: var(--spacing-lg);
        }

        .section-title {
            font-size: var(--font-size-2xl);
        }

        .section-subtitle {
            font-size: var(--font-size-base);
        }
    }

    /* Tablet Portrait (481px - 767px) */
    @media (max-width: 767px) {
        .container {
            max-width: 720px;
            padding: 0 var(--spacing-md);
        }

        .grid-cols-5,
        .grid-cols-4 {
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }



        .categories-grid {
            grid-template-columns: 1fr;
            gap: var(--spacing-md);
        }

        .hero {
            min-height: 90vh;
            padding: 0 0 var(--spacing-lg) 0;
        }

        .hero-content {
            grid-template-columns: 1fr;
            gap: var(--spacing-xl);
            text-align: center;
        }

        .hero-title-main {
            font-size: var(--font-size-xl);
        }

        .hero-title-accent {
            font-size: var(--font-size-4xl);
        }

        .hero-subtitle {
            font-size: var(--font-size-base);
            max-width: 100%;
        }

        .hero-stats {
            justify-content: center;
            gap: var(--spacing-lg);
            flex-wrap: wrap;
        }

        .hero-actions {
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-md);
        }

        .hero-cta-primary,
        .hero-cta-secondary {
            width: 100%;
            max-width: 320px;
            justify-content: center;
            padding: var(--spacing-lg) var(--spacing-xl);
        }

        .hero-features {
            justify-content: center;
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }

        .hero-main-visual {
            height: 350px;
        }

        .hero-product-showcase {
            width: 300px;
            height: 300px;
        }

        .hero-product-card {
            width: 100px;
            height: 120px;
            padding: var(--spacing-sm);
        }

        .hero-product-card .product-image {
            width: 50px;
            height: 50px;
            font-size: var(--font-size-lg);
        }

        .hero-trust-indicators {
            justify-content: center;
            flex-wrap: wrap;
            gap: var(--spacing-sm);
        }

        /* Section adjustments */
        .section {
            padding: var(--spacing-xl) 0;
        }

        .section-header {
            margin-bottom: var(--spacing-lg);
            text-align: center;
        }

        .section-title {
            font-size: var(--font-size-xl);
        }

        .section-subtitle {
            font-size: var(--font-size-sm);
        }

        /* Product card adjustments */
        .product-card {
            border-radius: var(--radius-md);
        }

        .compact-card .product-image {
            height: 180px;
        }

        .compact-card .product-content {
            padding: var(--spacing-sm);
        }

        .compact-card .product-title {
            font-size: var(--font-size-sm);
            min-height: 2.6em;
        }

        .compact-card .product-price {
            font-size: var(--font-size-base);
        }

        .compact-card .product-actions .btn {
            font-size: var(--font-size-sm);
            padding: var(--spacing-sm) var(--spacing-md);
        }



        /* Newsletter section */
        .newsletter-section {
            padding: var(--spacing-xl) 0;
        }

        .newsletter-form {
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .newsletter-form input {
            width: 100%;
        }

        .newsletter-form .btn {
            width: 100%;
        }
    }

    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .newsletter-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .newsletter-form {
        flex-direction: column;
    }

    /* Responsive Product Grids */
    .grid-cols-5,
    .grid-cols-4 {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }

    .compact-card .product-image {
        height: 180px;
    }

    .compact-card .product-content {
        padding: var(--spacing-sm);
    }

    /* Mobile Landscape (481px - 640px) */
    @media (max-width: 640px) {
        .container {
            padding: 0 var(--spacing-sm);
        }

        .grid-cols-5,
        .grid-cols-4 {
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
        }



        .hero-cta-primary,
        .hero-cta-secondary {
            max-width: 280px;
        }

        .section {
            padding: var(--spacing-lg) 0;
        }
    }

    /* Mobile Portrait (320px - 480px) */
    @media (max-width: 480px) {
        .container {
            padding: 0 var(--spacing-sm);
            max-width: 100%;
        }

        /* Hero Section Mobile Optimization */
        .hero {
            min-height: 80vh;
            padding: 0 0 var(--spacing-md) 0;
        }

        .hero-content {
            gap: var(--spacing-lg);
            padding: var(--spacing-md) 0;
        }

        .hero-badge {
            font-size: var(--font-size-xs);
            padding: var(--spacing-xs) var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .hero-title-main {
            font-size: var(--font-size-lg);
            margin-bottom: var(--spacing-xs);
        }

        .hero-title-accent {
            font-size: var(--font-size-3xl);
            margin-bottom: var(--spacing-sm);
        }

        .hero-subtitle {
            font-size: var(--font-size-sm);
            line-height: 1.5;
            margin-bottom: var(--spacing-lg);
        }

        .hero-stats {
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .hero-stat {
            min-width: 80px;
        }

        .hero-stat-number {
            font-size: var(--font-size-xl);
        }

        .hero-stat-label {
            font-size: var(--font-size-xs);
        }

        .hero-actions {
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-lg);
        }

        .hero-cta-primary,
        .hero-cta-secondary {
            padding: var(--spacing-md) var(--spacing-lg);
            font-size: var(--font-size-sm);
            max-width: 100%;
            min-height: 48px;
        }

        .hero-features {
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }

        .hero-feature {
            font-size: var(--font-size-xs);
        }

        .hero-main-visual {
            height: 250px;
        }

        .hero-product-showcase {
            width: 200px;
            height: 200px;
        }

        .hero-product-card {
            width: 80px;
            height: 100px;
            padding: var(--spacing-xs);
        }

        .hero-product-card .product-image {
            width: 40px;
            height: 40px;
            font-size: var(--font-size-base);
        }

        .hero-product-card .product-name {
            font-size: 10px;
        }

        .hero-product-card .product-price {
            font-size: var(--font-size-xs);
        }

        .floating-element {
            font-size: var(--font-size-base);
        }

        .trust-indicator {
            font-size: var(--font-size-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
        }

        .hero-scroll-indicator {
            bottom: var(--spacing-md);
            font-size: var(--font-size-xs);
        }

        .scroll-arrow {
            width: 30px;
            height: 30px;
        }

        /* Section Optimizations */
        .section {
            padding: var(--spacing-lg) 0;
        }

        .section-header {
            margin-bottom: var(--spacing-md);
        }

        .section-title {
            font-size: var(--font-size-lg);
            line-height: 1.3;
        }

        .section-subtitle {
            font-size: var(--font-size-xs);
            line-height: 1.4;
        }

        /* Grid Layouts */
        .grid-cols-5,
        .grid-cols-4 {
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
        }

        .categories-grid {
            grid-template-columns: 1fr;
            gap: var(--spacing-sm);
        }



        /* Product Cards Mobile Optimization */
        .product-card {
            border-radius: var(--radius-sm);
            min-height: 280px;
        }

        .compact-card .product-image {
            height: 140px;
        }

        .compact-card .product-content {
            padding: var(--spacing-xs);
        }

        .compact-card .product-title {
            font-size: var(--font-size-xs);
            min-height: 2.4em;
            line-height: 1.2;
        }

        .compact-card .product-price {
            font-size: var(--font-size-sm);
            font-weight: 700;
        }

        .compact-card .product-actions {
            margin-top: var(--spacing-xs);
        }

        .compact-card .product-actions .btn {
            font-size: var(--font-size-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
            min-height: 36px;
        }



        /* Newsletter Section */
        .newsletter-section {
            padding: var(--spacing-lg) 0;
        }

        .newsletter-content {
            text-align: center;
        }

        .newsletter-title {
            font-size: var(--font-size-lg);
            margin-bottom: var(--spacing-sm);
        }

        .newsletter-description {
            font-size: var(--font-size-xs);
            margin-bottom: var(--spacing-lg);
        }

        .newsletter-form {
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .newsletter-form input {
            width: 100%;
            padding: var(--spacing-md);
            font-size: var(--font-size-sm);
            min-height: 48px;
        }

        .newsletter-form .btn {
            width: 100%;
            padding: var(--spacing-md);
            font-size: var(--font-size-sm);
            min-height: 48px;
        }
    }

    /* Extra Small Mobile (320px and below) */
    @media (max-width: 320px) {
        .container {
            padding: 0 var(--spacing-xs);
        }

        .hero {
            min-height: 75vh;
            padding: 0 0 var(--spacing-sm) 0;
        }

        .hero-title-accent {
            font-size: var(--font-size-2xl);
        }

        .hero-stats {
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .hero-stat {
            min-width: auto;
        }

        .grid-cols-5,
        .grid-cols-4 {
            grid-template-columns: 1fr;
            gap: var(--spacing-xs);
        }

        .product-card {
            min-height: 260px;
        }

        .compact-card .product-image {
            height: 120px;
        }

        .section {
            padding: var(--spacing-md) 0;
        }

        .section-title {
            font-size: var(--font-size-base);
        }


    }

    /* Touch Interface Enhancements */

    /* Ensure all interactive elements meet minimum touch target size */
    .btn,
    .mobile-action-btn,
    .search-toggle,
    .modal-close,
    .quantity-btn,
    .product-card .product-actions .btn,
    .overlay-actions .btn {
        min-height: 44px;
        min-width: 44px;
        position: relative;
        overflow: hidden;
    }

    /* Enhanced touch feedback for buttons */
    .btn {
        transition: all 0.2s ease;
        -webkit-tap-highlight-color: transparent;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
    }

    .btn:active {
        transform: scale(0.98);
    }

    /* Touch ripple effect */
    .btn::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        transition: width 0.3s, height 0.3s;
        pointer-events: none;
        z-index: 1;
    }

    .btn:active::before {
        width: 100px;
        height: 100px;
    }

    /* Enhanced spacing for touch targets */
    .hero-actions {
        gap: var(--spacing-lg);
    }

    .hero-features {
        gap: var(--spacing-lg);
    }

    .product-actions {
        gap: var(--spacing-md);
        padding: var(--spacing-sm);
    }

    .overlay-actions {
        gap: var(--spacing-md);
    }

    /* Touch-friendly product cards */
    .product-card {
        cursor: pointer;
        -webkit-tap-highlight-color: transparent;
        transition: all 0.2s ease;
    }

    .product-card:active {
        transform: scale(0.98);
    }

    /* Enhanced mobile action buttons */
    .mobile-action-btn {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--font-size-lg);
        transition: all 0.2s ease;
        backdrop-filter: blur(10px);
        -webkit-tap-highlight-color: transparent;
    }

    .mobile-action-btn:active {
        transform: scale(0.9);
        background: var(--primary-purple);
    }



    /* Enhanced search toggle */
    .search-toggle {
        width: 56px;
        height: 56px;
        transition: all 0.2s ease;
        -webkit-tap-highlight-color: transparent;
    }

    .search-toggle:active {
        transform: scale(0.9);
    }

    /* Touch-friendly modal controls */
    .modal-close {
        width: 48px;
        height: 48px;
        transition: all 0.2s ease;
        -webkit-tap-highlight-color: transparent;
    }

    .modal-close:active {
        transform: scale(0.9);
    }

    .quantity-btn {
        width: 48px;
        height: 48px;
        transition: all 0.2s ease;
        -webkit-tap-highlight-color: transparent;
    }

    .quantity-btn:active {
        transform: scale(0.9);
        background: var(--primary-purple);
        color: white;
    }

    /* Touch feedback for form elements */
    input,
    textarea,
    select {
        min-height: 48px;
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
        transition: all 0.2s ease;
        -webkit-tap-highlight-color: transparent;
    }

    input:focus,
    textarea:focus,
    select:focus {
        transform: scale(1.02);
        box-shadow: 0 0 0 3px rgba(126, 87, 194, 0.2);
    }

    /* Enhanced touch targets for small elements */
    .suggestion-tag {
        min-height: 44px;
        padding: var(--spacing-sm) var(--spacing-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        -webkit-tap-highlight-color: transparent;
    }

    .suggestion-tag:active {
        transform: scale(0.95);
    }

    .trust-indicator {
        min-height: 44px;
        padding: var(--spacing-sm) var(--spacing-lg);
        display: flex;
        align-items: center;
        transition: all 0.2s ease;
        -webkit-tap-highlight-color: transparent;
    }

    /* Enhanced Product Cards */
    .featured-card,
    .latest-card {
        position: relative;
        overflow: hidden;
        border: 1px solid var(--border-color);
        transition: all var(--transition-fast);
    }

    .featured-card {
        border-color: var(--primary-purple);
        box-shadow: 0 4px 20px rgba(126, 87, 194, 0.1);
    }

    .featured-card:hover {
        border-color: var(--primary-light);
        box-shadow: 0 8px 30px rgba(126, 87, 194, 0.2);
        transform: translateY(-8px);
    }

    .latest-card {
        border-color: var(--success-color);
        box-shadow: 0 4px 20px rgba(16, 185, 129, 0.1);
    }

    .latest-card:hover {
        border-color: var(--success-color);
        box-shadow: 0 8px 30px rgba(16, 185, 129, 0.2);
        transform: translateY(-8px);
    }

    /* Enhanced Product Badges */
    .product-badge {
        position: absolute;
        top: var(--spacing-sm);
        left: var(--spacing-sm);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-sm);
        font-size: var(--font-size-xs);
        font-weight: 600;
        z-index: 2;
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .sale-badge {
        background: linear-gradient(135deg, var(--error-color), #dc2626);
        color: white;
        animation: pulse 2s infinite;
    }

    .featured-badge {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        color: white;
        top: var(--spacing-sm);
        right: var(--spacing-sm);
        left: auto;
    }

    .new-badge {
        background: linear-gradient(135deg, var(--success-color), #059669);
        color: white;
        animation: bounce 2s infinite;
    }

    /* Product Ratings */
    .product-rating {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        margin-bottom: var(--spacing-sm);
    }

    .stars {
        display: flex;
        gap: 2px;
    }

    .stars i {
        font-size: var(--font-size-xs);
        color: var(--text-tertiary);
    }

    .stars i.filled {
        color: #fbbf24;
    }

    .rating-count {
        font-size: var(--font-size-xs);
        color: var(--text-secondary);
    }

    /* Enhanced Product Overlay */
    .product-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        opacity: 0;
        transition: all var(--transition-fast);
        backdrop-filter: blur(2px);
    }

    .product-card:hover .product-overlay {
        opacity: 1;
    }

    .product-overlay .btn {
        transform: translateY(20px);
        transition: all var(--transition-fast);
    }

    .product-card:hover .product-overlay .btn {
        transform: translateY(0);
    }

    .product-overlay .btn:nth-child(1) {
        transition-delay: 0.1s;
    }

    .product-overlay .btn:nth-child(2) {
        transition-delay: 0.2s;
    }

    .product-overlay .btn:nth-child(3) {
        transition-delay: 0.3s;
    }

    /* Enhanced Product Content */
    .product-category {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        color: var(--text-secondary);
        font-size: var(--font-size-xs);
        font-weight: 500;
        margin-bottom: var(--spacing-xs);
    }

    .product-category i {
        color: var(--primary-purple);
        font-size: var(--font-size-xs);
    }

    .discount-amount {
        background: var(--success-color);
        color: white;
        padding: 2px var(--spacing-xs);
        border-radius: var(--radius-xs);
        font-size: var(--font-size-xs);
        font-weight: 600;
    }

    /* Enhanced View All Button */
    .view-all-btn {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-light));
        border: none;
        color: white;
        padding: var(--spacing-lg) var(--spacing-2xl);
        border-radius: var(--radius-xl);
        font-weight: 600;
        transition: all var(--transition-fast);
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
        box-shadow: 0 4px 15px rgba(126, 87, 194, 0.3);
    }

    .view-all-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(126, 87, 194, 0.4);
        color: white;
    }

    /* Animations */
    @keyframes pulse {

        0%,
        100% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.05);
        }
    }

    @keyframes bounce {

        0%,
        20%,
        50%,
        80%,
        100% {
            transform: translateY(0);
        }

        40% {
            transform: translateY(-5px);
        }

        60% {
            transform: translateY(-3px);
        }
    }

    .fade-in {
        animation: fadeInUp 0.6s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Mobile Touch Enhancements */
    @media (max-width: 768px) {

        /* Enhanced touch-friendly elements */
        .btn,
        .mobile-action-btn,
        .search-toggle,
        .modal-close,
        .quantity-btn {
            min-height: 48px;
            min-width: 48px;
        }

        .mobile-swipe-indicator {
            display: block;
        }

        .modal-body {
            grid-template-columns: 1fr;
            gap: var(--spacing-lg);
            padding: var(--spacing-lg);
        }

        .modal-content {
            margin: var(--spacing-sm);
            max-height: 95vh;
        }

        .modal-actions {
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .modal-actions .btn {
            min-height: 48px;
            font-size: var(--font-size-base);
        }

        .modal-product-features {
            padding: var(--spacing-md);
        }

        .enhanced-card {
            border-radius: var(--radius-md);
        }

        .enhanced-card:hover {
            transform: none;
            box-shadow: 0 4px 15px rgba(126, 87, 194, 0.1);
        }

        /* Enhanced touch-friendly product cards */
        .product-card {
            cursor: pointer;
            -webkit-tap-highlight-color: transparent;
            min-height: 300px;
        }

        .product-card:active {
            transform: scale(0.98);
        }

        /* Improved mobile product grid */
        .products-grid {
            gap: var(--spacing-md);
            padding: 0 var(--spacing-sm);
        }

        .products-grid.grid-cols-5,
        .products-grid.grid-cols-4 {
            grid-template-columns: repeat(2, 1fr);
        }

        /* Enhanced mobile-optimized buttons */
        .btn {
            min-height: 48px;
            font-size: var(--font-size-base);
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--radius-md);
        }

        /* Touch-friendly spacing */
        .hero-actions .btn {
            margin-bottom: var(--spacing-sm);
        }

        .product-actions {
            padding: var(--spacing-md);
            gap: var(--spacing-md);
        }

        .product-actions .btn {
            min-height: 44px;
            padding: var(--spacing-sm) var(--spacing-md);
        }

        /* Enhanced swipe gesture support */
        .products-grid {
            overflow-x: auto;
            scroll-snap-type: x mandatory;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
            -ms-overflow-style: none;
            scroll-behavior: smooth;
        }

        .products-grid::-webkit-scrollbar {
            display: none;
        }

        .product-card {
            scroll-snap-align: start;
            flex-shrink: 0;
        }



        /* Enhanced mobile search */
        .mobile-search-input {
            min-height: 48px;
            font-size: 16px;
            /* Prevent zoom on iOS */
        }

        .search-submit {
            width: 48px;
            height: 48px;
        }

        /* Touch-friendly newsletter form */
        .newsletter-form input {
            min-height: 48px;
            font-size: 16px;
            /* Prevent zoom on iOS */
        }

        .newsletter-form .btn {
            min-height: 48px;
        }

        /* Prevent text selection on touch */
        .product-card,
        .btn {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* Enhanced touch targets for small screens */
        .hero-feature,
        .trust-indicator,
        .suggestion-tag {
            min-height: 44px;
            padding: var(--spacing-sm) var(--spacing-md);
        }
    }

    @media (max-width: 480px) {

        /* Extra touch-friendly enhancements for small screens */
        .btn {
            min-height: 50px;
            padding: var(--spacing-md) var(--spacing-lg);
        }

        .mobile-action-btn {
            width: 50px;
            height: 50px;
        }

        .search-toggle {
            width: 60px;
            height: 60px;
        }



        /* Larger touch targets for critical actions */
        .hero-cta-primary,
        .hero-cta-secondary {
            min-height: 52px;
            padding: var(--spacing-lg) var(--spacing-xl);
        }

        .modal-actions .btn {
            min-height: 50px;
            padding: var(--spacing-lg);
        }

        /* Enhanced spacing for touch */
        .product-actions {
            gap: var(--spacing-lg);
        }

        .hero-actions {
            gap: var(--spacing-lg);
        }

        .hero-features {
            gap: var(--spacing-lg);
        }
    }

    /* Content Adaptation for Mobile */

    /* Enhanced Typography for Mobile Readability */
    @media (max-width: 768px) {

        /* Improved text hierarchy */
        h1,
        .hero-title-accent {
            font-size: clamp(2rem, 8vw, 3rem);
            line-height: 1.2;
            letter-spacing: -0.02em;
        }

        h2,
        .section-title {
            font-size: clamp(1.5rem, 6vw, 2rem);
            line-height: 1.3;
            letter-spacing: -0.01em;
        }

        h3,
        .hero-title-main {
            font-size: clamp(1.25rem, 5vw, 1.5rem);
            line-height: 1.4;
        }

        p,
        .hero-subtitle,
        .section-subtitle {
            font-size: clamp(0.875rem, 4vw, 1rem);
            line-height: 1.6;
            max-width: 100%;
        }

        /* Enhanced readability */
        .hero-subtitle {
            margin-bottom: var(--spacing-lg);
            text-align: center;
            padding: 0 var(--spacing-md);
        }

        .section-subtitle {
            margin-bottom: var(--spacing-md);
            text-align: center;
            padding: 0 var(--spacing-sm);
        }

        /* Improved content spacing */
        .hero-content {
            padding: var(--spacing-lg) var(--spacing-md);
        }

        .section-header {
            padding: 0 var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        /* Better content hierarchy */
        .hero-badge {
            font-size: clamp(0.75rem, 3vw, 0.875rem);
            padding: var(--spacing-xs) var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .hero-stats {
            margin: var(--spacing-lg) 0;
            padding: 0 var(--spacing-md);
        }

        .hero-stat-number {
            font-size: clamp(1.5rem, 6vw, 2rem);
        }

        .hero-stat-label {
            font-size: clamp(0.75rem, 3vw, 0.875rem);
        }

        .hero-features {
            margin: var(--spacing-lg) 0;
            padding: 0 var(--spacing-md);
        }

        .hero-feature {
            font-size: clamp(0.75rem, 3vw, 0.875rem);
        }
    }

    /* Image Optimization for Mobile */
    @media (max-width: 768px) {

        /* Responsive images */
        img {
            max-width: 100%;
            height: auto;
            display: block;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            transition: transform 0.3s ease;
        }

        /* Lazy loading optimization */
        img[loading="lazy"] {
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        img[loading="lazy"].loaded {
            opacity: 1;
        }

        /* Placeholder for loading images */
        .product-image {
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            animation: loading-shimmer 1.5s infinite linear;
        }

        .product-image.loaded {
            background: none;
            animation: none;
        }

        /* Hero visual optimization */
        .hero-main-visual {
            height: clamp(200px, 50vw, 300px);
        }

        .hero-product-showcase {
            width: clamp(150px, 40vw, 250px);
            height: clamp(150px, 40vw, 250px);
        }

        .hero-product-card {
            width: clamp(60px, 15vw, 80px);
            height: clamp(80px, 20vw, 100px);
        }

        .hero-product-card .product-image {
            width: clamp(30px, 8vw, 40px);
            height: clamp(30px, 8vw, 40px);
        }
    }

    /* Content Layout Improvements */
    @media (max-width: 480px) {

        /* Ultra-mobile typography */
        .hero-title-accent {
            font-size: clamp(1.75rem, 10vw, 2.5rem);
            margin-bottom: var(--spacing-sm);
        }

        .hero-title-main {
            font-size: clamp(1rem, 5vw, 1.25rem);
            margin-bottom: var(--spacing-xs);
        }

        .hero-subtitle {
            font-size: clamp(0.875rem, 4vw, 1rem);
            line-height: 1.5;
            margin-bottom: var(--spacing-md);
        }

        /* Compact content spacing */
        .hero-content {
            padding: var(--spacing-md) var(--spacing-sm);
        }

        .section {
            padding: var(--spacing-md) 0;
        }

        .section-header {
            padding: 0 var(--spacing-sm);
            margin-bottom: var(--spacing-md);
        }

        .section-title {
            font-size: clamp(1.25rem, 7vw, 1.75rem);
            margin-bottom: var(--spacing-xs);
        }

        .section-subtitle {
            font-size: clamp(0.75rem, 4vw, 0.875rem);
            line-height: 1.4;
        }

        /* Optimized hero stats layout */
        .hero-stats {
            flex-direction: row;
            justify-content: space-around;
            gap: var(--spacing-sm);
            margin: var(--spacing-md) 0;
        }

        .hero-stat {
            text-align: center;
            min-width: auto;
            flex: 1;
        }

        .hero-stat-number {
            font-size: clamp(1.25rem, 6vw, 1.5rem);
            margin-bottom: var(--spacing-xs);
        }

        .hero-stat-label {
            font-size: clamp(0.625rem, 3vw, 0.75rem);
            line-height: 1.2;
        }

        /* Improved feature layout */
        .hero-features {
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-sm);
            margin: var(--spacing-md) 0;
        }

        .hero-feature {
            font-size: clamp(0.75rem, 3.5vw, 0.875rem);
            text-align: center;
        }

        /* Trust indicators optimization */
        .hero-trust-indicators {
            flex-direction: column;
            gap: var(--spacing-xs);
            align-items: center;
        }

        .trust-indicator {
            font-size: clamp(0.75rem, 3vw, 0.875rem);
            padding: var(--spacing-xs) var(--spacing-sm);
            min-height: 36px;
        }
    }

    /* Loading Animation */
    @keyframes loading-shimmer {
        0% {
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }

        100% {
            background-position: 20px 20px, 20px 30px, 30px 10px, 10px 20px;
        }
    }

    /* Accessibility Improvements */
    @media (prefers-reduced-motion: reduce) {
        .product-image {
            animation: none;
        }

        img {
            transition: none;
        }

        .hero-product-card {
            animation: none;
        }
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
        .hero-title-accent {
            background: none;
            color: var(--text-primary);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .product-badge {
            border: 2px solid white;
        }

        .trust-indicator {
            border: 1px solid var(--text-primary);
        }
    }

    /* Enhanced Categories Section Styles */
    .categories-section {
        padding: var(--section-padding-desktop) 0;
        background: var(--dark-surface);
        position: relative;
        overflow: hidden;
        margin-bottom: var(--spacing-lg);
    }

    .categories-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(ellipse at center, rgba(126, 87, 194, 0.05) 0%, transparent 70%);
        pointer-events: none;
    }

    .section-badge {
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
        background: linear-gradient(135deg, rgba(126, 87, 194, 0.1), rgba(126, 87, 194, 0.05));
        border: 1px solid rgba(126, 87, 194, 0.2);
        color: var(--primary-purple);
        padding: var(--spacing-sm) var(--spacing-lg);
        border-radius: var(--radius-full);
        font-size: var(--font-size-sm);
        font-weight: 600;
        margin-bottom: var(--spacing-lg);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: relative;
        z-index: 1;
        overflow: hidden;
        width: auto;
        max-width: 100%;
        box-sizing: border-box;
        contain: layout style;
    }

    .section-stats {
        display: flex;
        justify-content: center;
        gap: var(--spacing-xl);
        margin-top: var(--spacing-lg);
    }

    .stat-item {
        text-align: center;
    }

    .stat-number {
        display: block;
        font-size: var(--font-size-xl);
        font-weight: 800;
        color: var(--primary-purple);
        line-height: 1;
        margin-bottom: var(--spacing-xs);
    }

    .stat-label {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        font-weight: 500;
    }

    .categories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-xl);
        margin-top: var(--spacing-3xl);
    }

    .category-card {
        position: relative;
        border-radius: var(--radius-xl);
        overflow: hidden;
        transition: all var(--transition-normal);
        background: var(--dark-card);
        border: 1px solid var(--border-color);
    }

    .category-card.featured {
        border-color: var(--primary-purple);
        box-shadow: 0 0 30px rgba(126, 87, 194, 0.2);
    }

    .category-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .category-link {
        display: block;
        text-decoration: none;
        color: inherit;
        height: 100%;
    }

    .category-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
    }

    .category-pattern {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
            radial-gradient(circle at 20% 80%, rgba(126, 87, 194, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(126, 87, 194, 0.1) 0%, transparent 50%);
        opacity: 0;
        transition: opacity var(--transition-normal);
    }

    .category-card:hover .category-pattern {
        opacity: 1;
    }

    .category-glow {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 200px;
        height: 200px;
        transform: translate(-50%, -50%);
        border-radius: 50%;
        opacity: 0;
        transition: opacity var(--transition-normal);
    }

    .category-card:hover .category-glow {
        opacity: 0.3;
    }

    .category-content {
        position: relative;
        z-index: 2;
        padding: var(--spacing-xl);
        display: flex;
        align-items: center;
        gap: var(--spacing-lg);
        height: 100%;
    }

    .category-icon {
        width: 80px;
        height: 80px;
        background: rgba(126, 87, 194, 0.1);
        border-radius: var(--radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--font-size-2xl);
        position: relative;
        flex-shrink: 0;
        transition: all var(--transition-normal);
    }

    .category-card:hover .category-icon {
        transform: scale(1.1);
    }

    .icon-glow {
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        border-radius: 50%;
        opacity: 0;
        transition: opacity var(--transition-normal);
    }

    .category-card:hover .icon-glow {
        opacity: 1;
    }

    .category-info {
        flex: 1;
    }

    .category-name {
        font-size: var(--font-size-xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
        line-height: 1.2;
    }

    .category-description {
        font-size: var(--font-size-base);
        color: var(--text-secondary);
        line-height: 1.5;
        margin-bottom: var(--spacing-md);
    }

    .category-meta {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        flex-wrap: wrap;
    }

    .products-count {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        font-weight: 500;
    }

    .featured-badge {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        color: white;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-md);
        font-size: var(--font-size-xs);
        font-weight: 600;
    }

    .category-arrow {
        font-size: var(--font-size-lg);
        color: var(--primary-purple);
        transition: transform var(--transition-fast);
    }

    .category-card:hover .category-arrow {
        transform: translateX(5px);
    }

    .category-hover-effect {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(126, 87, 194, 0.1), transparent);
        opacity: 0;
        transition: opacity var(--transition-normal);
        z-index: 1;
    }

    .category-card:hover .category-hover-effect {
        opacity: 1;
    }

    .categories-footer {
        text-align: center;
        margin-top: var(--spacing-3xl);
    }

    .categories-footer .footer-text {
        font-size: var(--font-size-lg);
        color: var(--text-secondary);
        margin-bottom: var(--spacing-lg);
    }

    .view-all-btn {
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        color: white;
        text-decoration: none;
        padding: var(--spacing-lg) var(--spacing-xl);
        border-radius: var(--radius-xl);
        font-weight: 600;
        font-size: var(--font-size-base);
        transition: all var(--transition-normal);
        box-shadow: 0 8px 25px rgba(126, 87, 194, 0.3);
    }

    .view-all-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(126, 87, 194, 0.5);
        color: white;
    }

    /* Enhanced Featured Products Section Styles */
    .featured-section {
        padding: var(--section-padding-desktop) 0;
        background: var(--dark-bg);
        position: relative;
        margin-bottom: var(--spacing-lg);
    }

    .product-filter-tabs {
        display: flex;
        justify-content: center;
        gap: var(--spacing-md);
        margin-top: var(--spacing-xl);
        flex-wrap: wrap;
    }

    .filter-tab {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        background: transparent;
        border: 2px solid var(--border-color);
        color: var(--text-secondary);
        padding: var(--spacing-md) var(--spacing-lg);
        border-radius: var(--radius-xl);
        font-weight: 600;
        font-size: var(--font-size-sm);
        cursor: pointer;
        transition: all var(--transition-fast);
    }

    .filter-tab:hover,
    .filter-tab.active {
        border-color: var(--primary-purple);
        color: var(--primary-purple);
        background: rgba(126, 87, 194, 0.1);
    }

    .enhanced-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: var(--spacing-xl);
        margin-top: var(--spacing-3xl);
    }

    .enhanced-product-card {
        background: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        overflow: hidden;
        transition: all var(--transition-normal);
        position: relative;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .enhanced-product-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        border-color: rgba(126, 87, 194, 0.3);
    }

    .product-image-container {
        position: relative;
        overflow: hidden;
        aspect-ratio: 1;
    }

    .product-image {
        width: 100%;
        height: 100%;
        position: relative;
        overflow: hidden;
    }

    .main-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform var(--transition-normal);
    }

    .enhanced-product-card:hover .main-image {
        transform: scale(1.1);
    }

    .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(126, 87, 194, 0.1), rgba(0, 0, 0, 0.3));
        opacity: 0;
        transition: opacity var(--transition-normal);
    }

    .enhanced-product-card:hover .image-overlay {
        opacity: 1;
    }

    .product-badges {
        position: absolute;
        top: var(--spacing-md);
        left: var(--spacing-md);
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);
        z-index: 3;
    }

    .product-badge {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-md);
        font-size: var(--font-size-xs);
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .sale-badge {
        background: var(--error-color);
        color: white;
    }

    .bestseller-badge {
        background: linear-gradient(135deg, #ff6b35, #f7931e);
        color: white;
    }

    .stock-badge {
        background: var(--warning-color);
        color: white;
    }

    .product-quick-actions {
        position: absolute;
        top: var(--spacing-md);
        right: var(--spacing-md);
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);
        opacity: 0;
        transform: translateX(20px);
        transition: all var(--transition-normal);
        z-index: 3;
    }

    .enhanced-product-card:hover .product-quick-actions {
        opacity: 1;
        transform: translateX(0);
    }

    .quick-action-btn {
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.9);
        border: none;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-primary);
        cursor: pointer;
        transition: all var(--transition-fast);
        backdrop-filter: blur(10px);
    }

    .quick-action-btn:hover {
        background: var(--primary-purple);
        color: white;
        transform: scale(1.1);
    }

    .product-content {
        padding: var(--spacing-lg);
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .product-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: var(--spacing-md);
    }

    .product-category {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: var(--font-size-xs);
        color: var(--primary-purple);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .product-rating {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .stars {
        display: flex;
        gap: 2px;
    }

    .stars i {
        font-size: var(--font-size-xs);
        color: var(--border-color);
        transition: color var(--transition-fast);
    }

    .stars i.active {
        color: #ffd700;
    }

    .rating-text {
        font-size: var(--font-size-xs);
        color: var(--text-secondary);
        font-weight: 500;
    }

    .product-title {
        margin-bottom: var(--spacing-md);
    }

    .product-title a {
        color: var(--text-primary);
        text-decoration: none;
        font-size: var(--font-size-lg);
        font-weight: 700;
        line-height: 1.3;
        transition: color var(--transition-fast);
    }

    .product-title a:hover {
        color: var(--primary-purple);
    }

    .product-features {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);
        margin-bottom: var(--spacing-lg);
    }

    .feature-tag {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        font-weight: 500;
    }

    .feature-tag i {
        color: var(--success-color);
        font-size: var(--font-size-xs);
    }

    .product-pricing {
        margin-bottom: var(--spacing-lg);
    }

    .price-container {
        display: flex;
        align-items: baseline;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-xs);
    }

    .current-price {
        font-size: var(--font-size-xl);
        font-weight: 800;
        color: var(--primary-purple);
    }

    .original-price {
        font-size: var(--font-size-base);
        color: var(--text-secondary);
        text-decoration: line-through;
    }

    .savings-info {
        font-size: var(--font-size-sm);
        color: var(--success-color);
        font-weight: 600;
    }

    .product-actions {
        margin-top: auto;
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .btn-add-to-cart {
        width: 100%;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        color: white;
        border: none;
        padding: var(--spacing-md) var(--spacing-lg);
        border-radius: var(--radius-lg);
        font-weight: 600;
        font-size: var(--font-size-base);
        cursor: pointer;
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
    }

    .btn-add-to-cart:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(126, 87, 194, 0.4);
    }

    .btn-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        transition: opacity var(--transition-fast);
    }

    .btn-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        opacity: 0;
        transition: opacity var(--transition-fast);
    }

    .btn-add-to-cart.loading .btn-content {
        opacity: 0;
    }

    .btn-add-to-cart.loading .btn-loading {
        opacity: 1;
    }

    .secondary-actions {
        display: flex;
        justify-content: center;
    }

    .btn-secondary {
        background: transparent;
        color: var(--primary-purple);
        border: 2px solid var(--primary-purple);
        padding: var(--spacing-sm) var(--spacing-lg);
        border-radius: var(--radius-lg);
        font-weight: 600;
        font-size: var(--font-size-sm);
        cursor: pointer;
        transition: all var(--transition-fast);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .btn-secondary:hover {
        background: var(--primary-purple);
        color: white;
    }

    .section-footer {
        margin-top: var(--spacing-4xl);
        text-align: center;
    }

    .section-footer .footer-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-xl);
    }

    .section-footer .footer-text h3 {
        font-size: var(--font-size-xl);
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .section-footer .footer-text p {
        font-size: var(--font-size-base);
        color: var(--text-secondary);
        max-width: 600px;
    }

    .section-footer .footer-actions {
        display: flex;
        gap: var(--spacing-lg);
        flex-wrap: wrap;
        justify-content: center;
    }

    .btn-view-all,
    .btn-expert-help {
        display: flex;
        align-items: center;
        text-decoration: none;
        padding: var(--spacing-lg) var(--spacing-xl);
        border-radius: var(--radius-xl);
        font-weight: 600;
        font-size: var(--font-size-base);
        transition: all var(--transition-normal);
    }

    .btn-view-all {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        color: white;
        box-shadow: 0 8px 25px rgba(126, 87, 194, 0.3);
    }

    .btn-view-all:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(126, 87, 194, 0.5);
        color: white;
    }

    .btn-expert-help {
        background: transparent;
        color: var(--primary-purple);
        border: 2px solid var(--primary-purple);
    }

    .btn-expert-help:hover {
        background: var(--primary-purple);
        color: white;
    }

    .btn-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    /* Services Section Styles */
    .services-section {
        padding: var(--spacing-xl) 0;
        background: var(--dark-surface);
        position: relative;
    }

    .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: var(--spacing-xl);
        margin-top: var(--spacing-3xl);
    }

    .service-card {
        background: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
    }

    .service-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(126, 87, 194, 0.1), transparent);
        transition: left 0.5s ease;
    }

    .service-card:hover::before {
        left: 100%;
    }

    .service-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        border-color: rgba(126, 87, 194, 0.3);
    }

    .service-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        border-radius: var(--radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--font-size-2xl);
        color: white;
        margin-bottom: var(--spacing-lg);
        position: relative;
        overflow: hidden;
    }

    .icon-background {
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
        animation: pulse 3s ease-in-out infinite;
    }

    .service-title {
        font-size: var(--font-size-xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
        line-height: 1.3;
    }

    .service-description {
        font-size: var(--font-size-base);
        color: var(--text-secondary);
        line-height: 1.6;
        margin-bottom: var(--spacing-lg);
    }

    .service-features {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .service-features li {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        margin-bottom: var(--spacing-sm);
        font-weight: 500;
    }

    .service-features i {
        color: var(--success-color);
        font-size: var(--font-size-xs);
        width: 16px;
    }

    .service-stats {
        margin-top: var(--spacing-3xl);
        background: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
    }

    .stats-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-xl);
    }

    .stat-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        text-align: left;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: var(--font-size-lg);
        flex-shrink: 0;
    }

    .stat-content {
        flex: 1;
    }

    .stat-number {
        font-size: var(--font-size-2xl);
        font-weight: 800;
        color: var(--primary-purple);
        line-height: 1;
        margin-bottom: var(--spacing-xs);
    }

    .stat-label {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        font-weight: 500;
        line-height: 1.2;
    }

    /* About Section Styles */
    .about-section {
        padding: var(--section-padding-desktop) 0;
        background: var(--dark-bg);
        margin-bottom: var(--spacing-lg);
    }

    .about-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-4xl);
        align-items: center;
        position: relative;
        z-index: 1;
    }

    .about-text {
        position: relative;
        z-index: 2;
        overflow: hidden;
        width: 100%;
    }

    .about-section .section-badge {
        position: relative;
        z-index: 3;
        display: inline-flex;
        margin-bottom: var(--spacing-lg);
        width: auto;
        max-width: 100%;
        box-sizing: border-box;
    }

    .about-highlights {
        margin: var(--spacing-xl) 0;
    }

    .highlight-item {
        display: flex;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
        padding: var(--spacing-lg);
        background: rgba(126, 87, 194, 0.05);
        border-radius: var(--radius-lg);
        border-left: 4px solid var(--primary-purple);
    }

    .highlight-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: var(--font-size-lg);
        flex-shrink: 0;
    }

    .highlight-content h4 {
        font-size: var(--font-size-lg);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .highlight-content p {
        font-size: var(--font-size-base);
        color: var(--text-secondary);
        line-height: 1.5;
    }

    .about-actions {
        display: flex;
        gap: var(--spacing-lg);
        margin-top: var(--spacing-xl);
        flex-wrap: wrap;
    }

    .btn-learn-more,
    .btn-contact {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        text-decoration: none;
        padding: var(--spacing-lg) var(--spacing-xl);
        border-radius: var(--radius-xl);
        font-weight: 600;
        font-size: var(--font-size-base);
        transition: all var(--transition-normal);
    }

    .btn-learn-more {
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        color: white;
        box-shadow: 0 8px 25px rgba(126, 87, 194, 0.3);
    }

    .btn-learn-more:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(126, 87, 194, 0.5);
        color: white;
    }

    .btn-contact {
        background: transparent;
        color: var(--primary-purple);
        border: 2px solid var(--primary-purple);
    }

    .btn-contact:hover {
        background: var(--primary-purple);
        color: white;
    }

    .about-visual {
        position: relative;
    }

    .visual-container {
        position: relative;
        border-radius: var(--radius-xl);
        overflow: hidden;
    }

    .main-image {
        width: 100%;
        height: 400px;
        object-fit: cover;
        border-radius: var(--radius-xl);
    }

    .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(126, 87, 194, 0.2), rgba(0, 0, 0, 0.3));
        border-radius: var(--radius-xl);
    }

    .floating-stats {
        position: absolute;
        top: var(--spacing-lg);
        right: var(--spacing-lg);
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        padding: var(--spacing-md);
        border-radius: var(--radius-lg);
        text-align: center;
        min-width: 100px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .stat-card .stat-number {
        font-size: var(--font-size-xl);
        font-weight: 800;
        color: var(--primary-purple);
        line-height: 1;
        margin-bottom: var(--spacing-xs);
    }

    .stat-card .stat-label {
        font-size: var(--font-size-xs);
        color: var(--text-primary);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .certification-badges {
        position: absolute;
        bottom: var(--spacing-lg);
        left: var(--spacing-lg);
        display: flex;
        gap: var(--spacing-sm);
    }

    .cert-badge {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--radius-md);
        font-size: var(--font-size-xs);
        font-weight: 600;
        color: var(--text-primary);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .cert-badge i {
        color: var(--primary-purple);
    }

    /* Testimonials Section Styles */
    .testimonials-section {
        padding: var(--section-padding-desktop) 0;
        background: var(--dark-surface);
        position: relative;
        margin-bottom: var(--spacing-lg);
    }

    .testimonials-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: var(--spacing-xl);
        margin-top: var(--spacing-3xl);
    }

    .testimonial-card {
        background: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
    }

    .testimonial-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        border-color: rgba(126, 87, 194, 0.3);
    }

    .testimonial-header {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }

    .customer-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        overflow: hidden;
        flex-shrink: 0;
        border: 3px solid var(--primary-purple);
    }

    .customer-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .customer-info {
        flex: 1;
    }

    .customer-name {
        font-size: var(--font-size-lg);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
    }

    .customer-title {
        font-size: var(--font-size-sm);
        color: var(--primary-purple);
        font-weight: 600;
        margin-bottom: 2px;
    }

    .customer-company {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        font-weight: 500;
    }

    .testimonial-rating {
        display: flex;
        align-items: center;
    }

    .testimonial-rating .stars {
        display: flex;
        gap: 2px;
    }

    .testimonial-rating .stars i {
        color: #ffd700;
        font-size: var(--font-size-sm);
    }

    .testimonial-content {
        position: relative;
    }

    .quote-icon {
        position: absolute;
        top: -10px;
        left: -10px;
        width: 40px;
        height: 40px;
        background: var(--primary-purple);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: var(--font-size-base);
    }

    .testimonial-text {
        font-size: var(--font-size-base);
        color: var(--text-secondary);
        line-height: 1.7;
        margin-bottom: var(--spacing-lg);
        padding-left: var(--spacing-lg);
        font-style: italic;
    }

    .testimonial-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: var(--spacing-md);
        border-top: 1px solid var(--border-color);
    }

    .verified-badge {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: var(--font-size-xs);
        color: var(--success-color);
        font-weight: 600;
    }

    .purchase-date {
        font-size: var(--font-size-xs);
        color: var(--text-secondary);
        font-weight: 500;
    }

    .testimonials-summary {
        margin-top: var(--spacing-4xl);
        text-align: center;
    }

    .summary-stats {
        display: flex;
        justify-content: center;
        gap: var(--spacing-3xl);
        margin-bottom: var(--spacing-xl);
        flex-wrap: wrap;
    }

    .summary-stat {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
    }

    .summary-stat .stat-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: var(--font-size-base);
    }

    .summary-stat .stat-content {
        text-align: left;
    }

    .summary-stat .stat-number {
        font-size: var(--font-size-xl);
        font-weight: 800;
        color: var(--primary-purple);
        line-height: 1;
        margin-bottom: var(--spacing-xs);
    }

    .summary-stat .stat-label {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        font-weight: 500;
    }

    .testimonials-cta {
        margin-top: var(--spacing-xl);
    }

    .testimonials-cta p {
        font-size: var(--font-size-lg);
        color: var(--text-secondary);
        margin-bottom: var(--spacing-lg);
    }

    .btn-testimonials-cta {
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        color: white;
        text-decoration: none;
        padding: var(--spacing-lg) var(--spacing-xl);
        border-radius: var(--radius-xl);
        font-weight: 600;
        font-size: var(--font-size-base);
        transition: all var(--transition-normal);
        box-shadow: 0 8px 25px rgba(126, 87, 194, 0.3);
    }

    .btn-testimonials-cta:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(126, 87, 194, 0.5);
        color: white;
    }

    /* Trust Section Styles */
    .trust-section {
        padding: var(--section-padding-desktop) 0;
        background: var(--dark-bg);
        margin-bottom: var(--spacing-lg);
    }

    .trust-indicators {
        margin-top: var(--spacing-3xl);
    }

    .trust-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-xl);
    }

    .trust-card {
        background: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        text-align: center;
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
    }

    .trust-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(126, 87, 194, 0.1), transparent);
        transition: left 0.5s ease;
    }

    .trust-card:hover::before {
        left: 100%;
    }

    .trust-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        border-color: rgba(126, 87, 194, 0.3);
    }

    .trust-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--font-size-2xl);
        color: white;
        margin: 0 auto var(--spacing-lg);
    }

    .trust-title {
        font-size: var(--font-size-xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .trust-description {
        font-size: var(--font-size-base);
        color: var(--text-secondary);
        line-height: 1.6;
        margin-bottom: var(--spacing-lg);
    }

    .trust-badge {
        display: inline-block;
        background: rgba(126, 87, 194, 0.1);
        color: var(--primary-purple);
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--radius-md);
        font-size: var(--font-size-sm);
        font-weight: 600;
        border: 1px solid rgba(126, 87, 194, 0.2);
    }

    .security-badges {
        margin-top: var(--spacing-3xl);
        text-align: center;
    }

    .badges-container {
        display: flex;
        justify-content: center;
        gap: var(--spacing-xl);
        flex-wrap: wrap;
    }

    .security-badge {
        opacity: 0.7;
        transition: opacity var(--transition-fast);
        filter: grayscale(100%);
    }

    .security-badge:hover {
        opacity: 1;
        filter: grayscale(0%);
    }

    .security-badge img {
        height: 60px;
        width: auto;
    }

    /* Newsletter Section Styles */
    .newsletter-section {
        padding: var(--section-padding-desktop) 0;
        background: var(--dark-surface);
        position: relative;
        overflow: hidden;
        margin-bottom: 0;
    }

    .newsletter-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
    }

    .newsletter-pattern {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
            radial-gradient(circle at 25% 25%, rgba(126, 87, 194, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(126, 87, 194, 0.1) 0%, transparent 50%);
    }

    .newsletter-glow {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 600px;
        height: 600px;
        transform: translate(-50%, -50%);
        background: radial-gradient(circle, rgba(126, 87, 194, 0.1) 0%, transparent 70%);
    }

    .newsletter-content {
        position: relative;
        z-index: 2;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-4xl);
        align-items: center;
    }

    .newsletter-benefits {
        margin-top: var(--spacing-xl);
    }

    .newsletter-benefits .benefit-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: var(--font-size-base);
        color: var(--text-secondary);
        margin-bottom: var(--spacing-md);
        font-weight: 500;
    }

    .newsletter-benefits .benefit-item i {
        color: var(--success-color);
        font-size: var(--font-size-base);
        width: 20px;
    }

    .newsletter-form-container {
        background: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-xl);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .newsletter-form .form-group {
        margin-bottom: var(--spacing-lg);
    }

    .input-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        margin-bottom: var(--spacing-md);
    }

    .newsletter-input {
        width: 100%;
        padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-lg) 50px;
        border: 2px solid var(--border-color);
        border-radius: var(--radius-lg);
        background: var(--dark-surface);
        color: var(--text-primary);
        font-size: var(--font-size-base);
        transition: all var(--transition-fast);
    }

    .newsletter-input:focus {
        outline: none;
        border-color: var(--primary-purple);
        box-shadow: 0 0 0 3px rgba(126, 87, 194, 0.1);
    }

    .input-icon {
        position: absolute;
        left: var(--spacing-lg);
        color: var(--text-secondary);
        font-size: var(--font-size-base);
        pointer-events: none;
    }

    .newsletter-btn {
        width: 100%;
        background: linear-gradient(135deg, var(--primary-purple), var(--primary-purple-dark));
        color: white;
        border: none;
        padding: var(--spacing-lg) var(--spacing-xl);
        border-radius: var(--radius-lg);
        font-weight: 600;
        font-size: var(--font-size-base);
        cursor: pointer;
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
    }

    .newsletter-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(126, 87, 194, 0.4);
    }

    .btn-text {
        transition: opacity var(--transition-fast);
    }

    .btn-loading {
        position: absolute;
        opacity: 0;
        transition: opacity var(--transition-fast);
    }

    .newsletter-btn.loading .btn-text {
        opacity: 0;
    }

    .newsletter-btn.loading .btn-loading {
        opacity: 1;
    }

    .btn-arrow {
        transition: transform var(--transition-fast);
    }

    .newsletter-btn:hover .btn-arrow {
        transform: translateX(5px);
    }

    .form-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: var(--spacing-md);
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
    }

    .privacy-notice,
    .subscriber-count {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .newsletter-success {
        text-align: center;
        padding: var(--spacing-xl);
    }

    .success-icon {
        width: 80px;
        height: 80px;
        background: var(--success-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--font-size-2xl);
        color: white;
        margin: 0 auto var(--spacing-lg);
    }

    .newsletter-success h3 {
        font-size: var(--font-size-xl);
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .newsletter-success p {
        font-size: var(--font-size-base);
        color: var(--text-secondary);
    }

    /* Enhanced Tablet Responsiveness */
    @media (max-width: 1024px) {
        :root {
            --section-padding-tablet: var(--spacing-2xl);
        }

        .section-enhanced {
            padding: var(--section-padding-tablet) 0;
        }

        .section-header-enhanced {
            margin-bottom: var(--spacing-3xl);
        }

        .container-enhanced {
            max-width: 900px;
            padding: 0 var(--spacing-md);
        }

        .section-separator,
        .section-separator-thick,
        .section-separator-glow {
            margin: var(--spacing-2xl) 0;
        }
    }

    /* Enhanced Mobile Responsiveness for New Sections */
    @media (max-width: 768px) {

        /* Enhanced Spacing for Mobile */
        :root {
            --section-padding-mobile: var(--spacing-xl);
            --section-gap: var(--spacing-md);
        }

        .section-enhanced {
            padding: var(--section-padding-mobile) 0;
        }

        .section-header-enhanced {
            margin-bottom: var(--spacing-2xl);
            padding: 0 var(--spacing-md);
        }

        .container-enhanced {
            padding: 0 var(--spacing-md);
        }

        .section-separator,
        .section-separator-thick,
        .section-separator-glow {
            margin: var(--spacing-xl) 0;
        }

        /* Categories Section Mobile */
        .categories-grid {
            grid-template-columns: 1fr;
            gap: var(--spacing-lg);
        }

        .category-content {
            flex-direction: column;
            text-align: center;
            gap: var(--spacing-md);
        }

        .category-icon {
            width: 60px;
            height: 60px;
            font-size: var(--font-size-xl);
        }

        .section-stats {
            flex-direction: column;
            gap: var(--spacing-md);
        }

        /* Featured Products Mobile */
        .product-filter-tabs {
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .filter-tab {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }

        .enhanced-grid {
            grid-template-columns: 1fr;
            gap: var(--spacing-lg);
        }

        .section-footer .footer-actions {
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .btn-view-all,
        .btn-expert-help {
            width: 100%;
            justify-content: center;
        }

        /* Services Section Mobile */
        .services-grid {
            grid-template-columns: 1fr;
            gap: var(--spacing-lg);
        }

        .stats-container {
            grid-template-columns: 1fr;
            gap: var(--spacing-lg);
        }

        /* About Section Mobile */
        .about-content {
            grid-template-columns: 1fr;
            gap: var(--spacing-xl);
        }

        .about-visual {
            order: -1;
        }

        .about-actions {
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .btn-learn-more,
        .btn-contact {
            width: 100%;
            justify-content: center;
        }

        .floating-stats {
            position: static;
            flex-direction: row;
            justify-content: center;
            margin-top: var(--spacing-lg);
        }

        .certification-badges {
            position: static;
            justify-content: center;
            margin-top: var(--spacing-lg);
        }

        /* Testimonials Mobile */
        .testimonials-grid {
            grid-template-columns: 1fr;
            gap: var(--spacing-lg);
        }

        .testimonial-header {
            flex-direction: column;
            text-align: center;
            gap: var(--spacing-sm);
        }

        .customer-avatar {
            align-self: center;
        }

        .summary-stats {
            flex-direction: column;
            gap: var(--spacing-lg);
        }

        .summary-stat {
            justify-content: center;
        }

        /* Trust Section Mobile */
        .trust-grid {
            grid-template-columns: 1fr;
            gap: var(--spacing-lg);
        }

        .badges-container {
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-md);
        }

        /* Newsletter Mobile */
        .newsletter-content {
            grid-template-columns: 1fr;
            gap: var(--spacing-xl);
            text-align: center;
        }

        .form-footer {
            flex-direction: column;
            gap: var(--spacing-sm);
            text-align: center;
        }

        /* General Mobile Improvements */
        .section-header {
            text-align: center;
            padding: 0 var(--spacing-md);
        }

        .section-title {
            font-size: var(--font-size-2xl);
        }

        .section-subtitle {
            font-size: var(--font-size-base);
        }

        .highlight-item {
            flex-direction: column;
            text-align: center;
            gap: var(--spacing-md);
        }

        .highlight-icon {
            align-self: center;
        }
    }

    @media (max-width: 480px) {

        /* Enhanced Spacing for Extra Small Screens */
        :root {
            --section-padding-mobile: var(--spacing-lg);
            --section-gap: var(--spacing-sm);
        }

        .section-enhanced {
            padding: var(--section-padding-mobile) 0;
        }

        .section-header-enhanced {
            margin-bottom: var(--spacing-xl);
            padding: 0 var(--spacing-sm);
        }

        .container-enhanced {
            padding: 0 var(--spacing-sm);
        }

        .section-separator,
        .section-separator-thick,
        .section-separator-glow {
            margin: var(--spacing-md) 0;
        }

        /* Extra small screens */
        .category-card,
        .enhanced-product-card,
        .service-card,
        .testimonial-card,
        .trust-card {
            margin: 0 var(--spacing-sm);
        }

        .section-badge {
            font-size: var(--font-size-xs);
            padding: var(--spacing-xs) var(--spacing-md);
        }

        .service-icon,
        .trust-icon {
            width: 60px;
            height: 60px;
            font-size: var(--font-size-xl);
        }

        .newsletter-input {
            padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) 40px;
        }

        .input-icon {
            left: var(--spacing-md);
            font-size: var(--font-size-sm);
        }

        .floating-stats {
            flex-direction: column;
            align-items: center;
        }

        .certification-badges {
            flex-direction: column;
            align-items: center;
        }
    }
</style>

<script>
    // Enhanced Product Interactions and Hero Functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Hero scroll indicator functionality
        const scrollIndicator = document.querySelector('.hero-scroll-indicator');
        if (scrollIndicator) {
            scrollIndicator.addEventListener('click', function() {
                const categoriesSection = document.querySelector('.categories-section');
                if (categoriesSection) {
                    categoriesSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        }

        // Parallax effect for hero background
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const hero = document.querySelector('.hero');
            const heroBackground = document.querySelector('.hero-background');

            if (hero && heroBackground) {
                const heroHeight = hero.offsetHeight;
                const scrollPercent = scrolled / heroHeight;

                if (scrollPercent <= 1) {
                    heroBackground.style.transform = `translateY(${scrolled * 0.5}px)`;
                    hero.style.opacity = Math.max(0.3, 1 - scrollPercent * 0.7);
                }
            }
        });

        // Animate hero stats on scroll
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        };

        const statsObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const statNumbers = entry.target.querySelectorAll('.hero-stat-number');
                    statNumbers.forEach(stat => {
                        const finalValue = stat.textContent;
                        const numericValue = parseInt(finalValue.replace(/[^\d]/g, ''));
                        const suffix = finalValue.replace(/[\d]/g, '');

                        animateNumber(stat, 0, numericValue, suffix, 2000);
                    });
                    statsObserver.unobserve(entry.target);
                }
            });
        }, observerOptions);

        const heroStats = document.querySelector('.hero-stats');
        if (heroStats) {
            statsObserver.observe(heroStats);
        }
        // Add to Cart functionality
        document.querySelectorAll('.add-to-cart, .add-to-cart-quick').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const productId = this.dataset.productId;
                const quantity = this.dataset.quantity || 1;

                addToCart(productId, quantity);
            });
        });

        // Add to Wishlist functionality
        document.querySelectorAll('.add-to-wishlist').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const productId = this.dataset.productId;

                addToWishlist(productId);
            });
        });

        // Quick View functionality
        document.querySelectorAll('.quick-view').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const productId = this.dataset.productId;

                showQuickView(productId);
            });
        });

        // Staggered animation for product cards
        const productCards = document.querySelectorAll('.product-card');
        productCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
        });
    });

    // Add to Cart Function
    function addToCart(productId, quantity) {
        const formData = new FormData();
        formData.append('product_id', productId);
        formData.append('quantity', quantity);

        fetch('<?= UrlHelper::url('/cart/add') ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Product added to cart successfully!', 'success');
                    updateCartCount();
                } else {
                    showNotification(data.message || 'Failed to add to cart', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred while adding to cart', 'error');
            });
    }

    // Add to Wishlist Function
    function addToWishlist(productId) {
        const formData = new FormData();
        formData.append('product_id', productId);

        fetch('<?= UrlHelper::url('/wishlist/add') ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Product added to wishlist!', 'success');
                    updateWishlistCount();
                } else {
                    showNotification(data.message || 'Failed to add to wishlist', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred while adding to wishlist', 'error');
            });
    }

    // Quick View Function
    function showQuickView(productId) {
        // For now, redirect to product detail page
        // In a real implementation, this would show a modal
        window.location.href = `<?= UrlHelper::url('/product/') ?>${productId}`;
    }

    // Notification System
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            ${getNotificationStyle(type)}
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    function getNotificationIcon(type) {
        switch (type) {
            case 'success':
                return 'check-circle';
            case 'error':
                return 'exclamation-circle';
            case 'warning':
                return 'exclamation-triangle';
            default:
                return 'info-circle';
        }
    }

    function getNotificationStyle(type) {
        switch (type) {
            case 'success':
                return 'background: linear-gradient(135deg, #10b981, #059669);';
            case 'error':
                return 'background: linear-gradient(135deg, #ef4444, #dc2626);';
            case 'warning':
                return 'background: linear-gradient(135deg, #f59e0b, #d97706);';
            default:
                return 'background: linear-gradient(135deg, #3b82f6, #2563eb);';
        }
    }

    // Number animation function for hero stats
    function animateNumber(element, start, end, suffix, duration) {
        const startTime = performance.now();

        function updateNumber(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function for smooth animation
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const current = Math.floor(start + (end - start) * easeOutQuart);

            element.textContent = current + suffix;

            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        }

        requestAnimationFrame(updateNumber);
    }

    // Enhanced button hover effects
    function addButtonEffects() {
        const buttons = document.querySelectorAll('.hero-cta-primary, .hero-cta-secondary');

        buttons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px) scale(1.02)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    }

    // Initialize enhanced effects
    addButtonEffects();

    // Update counters (placeholder functions)
    function updateCartCount() {
        // This would typically fetch the current cart count from the server
        console.log('Cart updated');
    }

    function updateWishlistCount() {
        // This would typically fetch the current wishlist count from the server
        console.log('Wishlist updated');
    }



    function updateCartBadge() {
        const cartBadge = document.querySelector('.cart-badge');
        if (cartBadge) {
            // Simulate cart count - replace with actual implementation
            const cartCount = getCartCount();
            if (cartCount > 0) {
                cartBadge.textContent = cartCount > 99 ? '99+' : cartCount;
                cartBadge.style.display = 'flex';

                // Add pulse animation for new items
                cartBadge.style.animation = 'pulse 0.5s ease-in-out';
                setTimeout(() => {
                    cartBadge.style.animation = '';
                }, 500);
            } else {
                cartBadge.style.display = 'none';
            }
        }
    }

    function getCartCount() {
        // Placeholder - implement actual cart count logic
        // This could fetch from localStorage, sessionStorage, or make an API call
        return parseInt(localStorage.getItem('cartCount') || '0');
    }

    // Enhanced Mobile Search Functions
    function initMobileSearch() {
        const searchToggle = document.querySelector('.search-toggle');
        const searchPanel = document.querySelector('.search-panel');
        const searchClose = document.querySelector('.search-close');
        const searchInput = document.querySelector('.mobile-search-input');
        const searchSubmit = document.querySelector('.search-submit');
        const suggestionTags = document.querySelectorAll('.suggestion-tag');

        if (searchToggle && searchPanel) {
            // Enhanced search toggle with animation
            searchToggle.addEventListener('click', () => {
                searchPanel.classList.add('active');
                document.body.style.overflow = 'hidden';

                // Add entrance animation
                searchPanel.style.animation = 'slideInFromTop 0.3s ease-out';

                // Focus input with delay for better UX
                setTimeout(() => {
                    searchInput.focus();
                    if (navigator.vibrate) {
                        navigator.vibrate(30);
                    }
                }, 300);
            });

            searchClose.addEventListener('click', () => {
                searchPanel.style.animation = 'slideOutToTop 0.3s ease-in';

                setTimeout(() => {
                    searchPanel.classList.remove('active');
                    document.body.style.overflow = '';
                    searchInput.value = '';
                    searchPanel.style.animation = '';
                }, 300);
            });

            // Enhanced search functionality
            searchSubmit.addEventListener('click', () => {
                performSearch(searchInput.value);
            });

            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    performSearch(searchInput.value);
                }
            });

            // Real-time search suggestions (debounced)
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    if (e.target.value.length > 2) {
                        showSearchSuggestions(e.target.value);
                    }
                }, 300);
            });

            // Enhanced suggestion tags
            suggestionTags.forEach(tag => {
                tag.addEventListener('click', () => {
                    searchInput.value = tag.textContent;
                    performSearch(tag.textContent);

                    // Add visual feedback
                    tag.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        tag.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // Close on escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && searchPanel.classList.contains('active')) {
                    searchClose.click();
                }
            });

            // Close on outside click
            searchPanel.addEventListener('click', (e) => {
                if (e.target === searchPanel) {
                    searchClose.click();
                }
            });
        }
    }

    function performSearch(query) {
        if (query.trim()) {
            // Add loading state
            const searchSubmit = document.querySelector('.search-submit');
            const originalHTML = searchSubmit.innerHTML;
            searchSubmit.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            // Simulate search delay
            setTimeout(() => {
                window.location.href = `<?= UrlHelper::url('/products?search=') ?>${encodeURIComponent(query)}`;
            }, 500);
        }
    }

    function showSearchSuggestions(query) {
        // Placeholder for real-time search suggestions
        // This would typically make an API call to get suggestions
        console.log('Showing suggestions for:', query);
    }

    // Enhanced Image Lazy Loading and Performance
    function initImageOptimization() {
        // Enhanced lazy loading with Intersection Observer
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;

                        // Add loading class for smooth transition
                        img.classList.add('loading');

                        // Load the image
                        img.onload = () => {
                            img.classList.add('loaded');
                            img.classList.remove('loading');

                            // Mark parent container as loaded
                            const container = img.closest('.product-image');
                            if (container) {
                                container.classList.add('loaded');
                            }
                        };

                        img.onerror = () => {
                            img.classList.add('error');
                            img.src = '<?= UrlHelper::url('/assets/img/placeholder.jpg') ?>';
                        };

                        observer.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.1
            });

            // Observe all lazy images
            document.querySelectorAll('img[loading="lazy"]').forEach(img => {
                imageObserver.observe(img);
            });
        }

        // Preload critical images
        const criticalImages = document.querySelectorAll('.hero img, .featured-card img');
        criticalImages.forEach(img => {
            if (img.loading !== 'lazy') {
                const preloadLink = document.createElement('link');
                preloadLink.rel = 'preload';
                preloadLink.as = 'image';
                preloadLink.href = img.src;
                document.head.appendChild(preloadLink);
            }
        });
    }

    // Performance optimization for mobile
    function initMobilePerformance() {
        // Reduce animations on low-end devices
        if (navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 2) {
            document.documentElement.classList.add('low-performance');
        }

        // Optimize scroll performance
        let ticking = false;

        function updateScrollEffects() {
            // Throttled scroll effects
            const scrolled = window.pageYOffset;

            // Update parallax effects only if visible
            const hero = document.querySelector('.hero');
            if (hero && scrolled < hero.offsetHeight) {
                const heroBackground = document.querySelector('.hero-background');
                if (heroBackground) {
                    heroBackground.style.transform = `translateY(${scrolled * 0.3}px)`;
                }
            }

            ticking = false;
        }

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollEffects);
                ticking = true;
            }
        }, {
            passive: true
        });

        // Optimize touch events
        document.addEventListener('touchstart', () => {}, {
            passive: true
        });
        document.addEventListener('touchmove', () => {}, {
            passive: true
        });
    }

    // Content adaptation based on device capabilities
    function adaptContentForDevice() {
        const isLowEndDevice = navigator.hardwareConcurrency <= 2 ||
            navigator.deviceMemory <= 2 ||
            navigator.connection?.effectiveType === 'slow-2g' ||
            navigator.connection?.effectiveType === '2g';

        if (isLowEndDevice) {
            // Reduce visual effects for low-end devices
            document.documentElement.classList.add('reduced-effects');

            // Disable non-essential animations
            const style = document.createElement('style');
            style.textContent = `
                .reduced-effects .hero-floating-elements,
                .reduced-effects .floating-element,
                .reduced-effects .hero-particles {
                    display: none;
                }
                .reduced-effects .enhanced-card:hover {
                    transform: none;
                }
                .reduced-effects .product-overlay {
                    opacity: 1;
                    background: rgba(0, 0, 0, 0.6);
                }
            `;
            document.head.appendChild(style);
        }

        // Adapt grid based on screen size and performance
        const updateProductGrid = () => {
            const grids = document.querySelectorAll('.products-grid');
            const screenWidth = window.innerWidth;

            grids.forEach(grid => {
                if (screenWidth <= 320) {
                    grid.style.gridTemplateColumns = '1fr';
                } else if (screenWidth <= 480) {
                    grid.style.gridTemplateColumns = 'repeat(2, 1fr)';
                } else if (screenWidth <= 768) {
                    grid.style.gridTemplateColumns = 'repeat(2, 1fr)';
                } else if (screenWidth <= 1024) {
                    grid.style.gridTemplateColumns = 'repeat(3, 1fr)';
                }
            });
        };

        updateProductGrid();
        window.addEventListener('resize', updateProductGrid);
    }

    // Initialize all optimizations
    initImageOptimization();
    initMobilePerformance();
    adaptContentForDevice();

    // Initialize mobile features
    if (window.innerWidth <= 768) {
        initMobileSearch();
    }

    // Re-initialize on window resize
    window.addEventListener('resize', () => {
        if (window.innerWidth <= 768) {
            initMobileSearch();
        }
        adaptContentForDevice();
    });

    // Performance monitoring and optimization
    function initPerformanceMonitoring() {
        // Monitor Core Web Vitals
        if ('web-vital' in window) {
            // This would integrate with web-vitals library if available
            console.log('Performance monitoring initialized');
        }

        // Monitor memory usage
        if (performance.memory) {
            const memoryInfo = performance.memory;
            if (memoryInfo.usedJSHeapSize / memoryInfo.totalJSHeapSize > 0.8) {
                // High memory usage - reduce effects
                document.documentElement.classList.add('memory-constrained');
            }
        }

        // Monitor frame rate
        let frameCount = 0;
        let lastTime = performance.now();

        function measureFPS() {
            frameCount++;
            const currentTime = performance.now();

            if (currentTime - lastTime >= 1000) {
                const fps = frameCount;
                frameCount = 0;
                lastTime = currentTime;

                // If FPS is low, reduce animations
                if (fps < 30) {
                    document.documentElement.classList.add('low-fps');
                }
            }

            requestAnimationFrame(measureFPS);
        }

        requestAnimationFrame(measureFPS);
    }

    // Optimize critical rendering path
    function optimizeCriticalPath() {
        // Preload critical resources
        const criticalResources = [
            '<?= UrlHelper::css('style.css') ?>',
            '<?= UrlHelper::url('/assets/fonts/main.woff2') ?>'
        ];

        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource;
            link.as = resource.includes('.css') ? 'style' : 'font';
            if (link.as === 'font') {
                link.type = 'font/woff2';
                link.crossOrigin = 'anonymous';
            }
            document.head.appendChild(link);
        });

        // Optimize font loading
        if ('fonts' in document) {
            document.fonts.ready.then(() => {
                document.documentElement.classList.add('fonts-loaded');
            });
        }
    }

    // Service Worker for caching (if supported)
    function initServiceWorker() {
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);

                        // Update available
                        registration.addEventListener('updatefound', () => {
                            const newWorker = registration.installing;
                            newWorker.addEventListener('statechange', () => {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // Show update notification
                                    showUpdateNotification();
                                }
                            });
                        });
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    }

    function showUpdateNotification() {
        const notification = document.createElement('div');
        notification.className = 'update-notification';
        notification.innerHTML = `
            <div class="update-content">
                <span>New version available!</span>
                <button onclick="window.location.reload()">Update</button>
                <button onclick="this.parentElement.parentElement.remove()">Later</button>
            </div>
        `;
        document.body.appendChild(notification);
    }

    // Initialize all performance optimizations
    initPerformanceMonitoring();
    optimizeCriticalPath();
    initServiceWorker();

    // Add CSS animations for search panel and performance styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInFromTop {
            from {
                transform: translateY(-100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes slideOutToTop {
            from {
                transform: translateY(0);
                opacity: 1;
            }
            to {
                transform: translateY(-100%);
                opacity: 0;
            }
        }



        /* Performance-based optimizations */
        .low-fps .enhanced-card:hover,
        .memory-constrained .enhanced-card:hover {
            transform: none;
        }

        .low-fps .hero-floating-elements,
        .memory-constrained .hero-floating-elements {
            display: none;
        }

        .fonts-loaded .hero-title-accent {
            font-display: swap;
        }

        /* Update notification */
        .update-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-purple);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 10000;
            animation: slideInFromRight 0.3s ease-out;
        }

        .update-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .update-content button {
            background: white;
            color: var(--primary-purple);
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.875rem;
        }

        @keyframes slideInFromRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Critical CSS optimizations */
        .hero {
            contain: layout style paint;
        }

        .product-card {
            contain: layout style;
        }

        /* Reduce motion for performance */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    `;
    document.head.appendChild(style);

    // Cross-Device Testing and Compatibility
    function initDeviceDetection() {
        const deviceInfo = {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            screenWidth: screen.width,
            screenHeight: screen.height,
            devicePixelRatio: window.devicePixelRatio || 1,
            orientation: screen.orientation?.type || 'unknown',
            touchSupport: 'ontouchstart' in window,
            connectionType: navigator.connection?.effectiveType || 'unknown',
            memory: navigator.deviceMemory || 'unknown',
            cores: navigator.hardwareConcurrency || 'unknown'
        };

        // Detect device type
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const isTablet = /iPad|Android(?!.*Mobile)/i.test(navigator.userAgent);
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        const isAndroid = /Android/i.test(navigator.userAgent);
        const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
        const isChrome = /Chrome/i.test(navigator.userAgent);
        const isFirefox = /Firefox/i.test(navigator.userAgent);

        // Add device classes to body
        document.body.classList.add(
            isMobile ? 'mobile-device' : 'desktop-device',
            isTablet ? 'tablet-device' : '',
            isIOS ? 'ios-device' : '',
            isAndroid ? 'android-device' : '',
            isSafari ? 'safari-browser' : '',
            isChrome ? 'chrome-browser' : '',
            isFirefox ? 'firefox-browser' : ''
        );

        // Device-specific optimizations
        if (isIOS) {
            // iOS-specific fixes
            document.documentElement.style.setProperty('--vh', `${window.innerHeight * 0.01}px`);

            // Handle iOS viewport changes
            window.addEventListener('resize', () => {
                document.documentElement.style.setProperty('--vh', `${window.innerHeight * 0.01}px`);
            });

            // Fix iOS input zoom
            const inputs = document.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                if (input.style.fontSize === '' || parseFloat(input.style.fontSize) < 16) {
                    input.style.fontSize = '16px';
                }
            });
        }

        if (isAndroid) {
            // Android-specific optimizations
            document.body.classList.add('android-device');

            // Handle Android keyboard
            let initialViewportHeight = window.innerHeight;
            window.addEventListener('resize', () => {
                const currentHeight = window.innerHeight;
                if (currentHeight < initialViewportHeight * 0.75) {
                    document.body.classList.add('keyboard-open');
                } else {
                    document.body.classList.remove('keyboard-open');
                }
            });
        }

        // Log device info for debugging
        console.log('Device Info:', deviceInfo);

        return deviceInfo;
    }

    // Orientation change handling
    function initOrientationHandling() {
        function handleOrientationChange() {
            const orientation = screen.orientation?.type ||
                (window.innerHeight > window.innerWidth ? 'portrait' : 'landscape');

            document.body.classList.remove('portrait', 'landscape');
            document.body.classList.add(orientation.includes('portrait') ? 'portrait' : 'landscape');

            // Recalculate layouts after orientation change
            setTimeout(() => {
                window.dispatchEvent(new Event('resize'));
            }, 100);
        }

        // Listen for orientation changes
        if (screen.orientation) {
            screen.orientation.addEventListener('change', handleOrientationChange);
        } else {
            window.addEventListener('orientationchange', handleOrientationChange);
        }

        // Initial orientation
        handleOrientationChange();
    }

    // Cross-browser compatibility fixes
    function initCompatibilityFixes() {
        // Polyfill for CSS custom properties (IE11)
        if (!window.CSS || !CSS.supports('color', 'var(--fake-var)')) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = '<?= UrlHelper::url('/assets/css/ie-fallback.css') ?>';
            document.head.appendChild(link);
        }

        // Intersection Observer polyfill
        if (!('IntersectionObserver' in window)) {
            const script = document.createElement('script');
            script.src = 'https://polyfill.io/v3/polyfill.min.js?features=IntersectionObserver';
            document.head.appendChild(script);
        }

        // Object-fit polyfill for IE
        if (!('objectFit' in document.documentElement.style)) {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/object-fit-images/3.2.4/ofi.min.js';
            script.onload = () => {
                if (window.objectFitImages) {
                    objectFitImages();
                }
            };
            document.head.appendChild(script);
        }

        // Fix for Safari's 100vh issue
        if (/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {
            const style = document.createElement('style');
            style.textContent = `
                .hero {
                    height: 100vh;
                    height: calc(var(--vh, 1vh) * 100);
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Performance testing utilities
    function initPerformanceTesting() {
        // Measure page load performance
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                const loadTime = perfData.loadEventEnd - perfData.fetchStart;
                const domContentLoaded = perfData.domContentLoadedEventEnd - perfData.fetchStart;

                console.log('Performance Metrics:', {
                    loadTime: `${loadTime}ms`,
                    domContentLoaded: `${domContentLoaded}ms`,
                    firstPaint: performance.getEntriesByType('paint')[0]?.startTime || 'N/A',
                    firstContentfulPaint: performance.getEntriesByType('paint')[1]?.startTime || 'N/A'
                });

                // Send performance data to analytics (if implemented)
                if (window.gtag) {
                    gtag('event', 'page_load_time', {
                        value: Math.round(loadTime),
                        custom_parameter: navigator.userAgent
                    });
                }
            }, 0);
        });

        // Monitor long tasks
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.duration > 50) {
                        console.warn('Long task detected:', entry);
                    }
                }
            });
            observer.observe({
                entryTypes: ['longtask']
            });
        }
    }

    // Debug utilities for testing
    function initDebugUtilities() {
        // Add debug panel for development
        if (window.location.hostname === 'localhost' || window.location.search.includes('debug=true')) {
            const debugPanel = document.createElement('div');
            debugPanel.id = 'debug-panel';
            debugPanel.style.cssText = `
                position: fixed;
                top: 10px;
                left: 10px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-family: monospace;
                font-size: 12px;
                z-index: 10000;
                max-width: 300px;
                display: none;
            `;

            const toggleButton = document.createElement('button');
            toggleButton.textContent = 'Debug';
            toggleButton.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                z-index: 10001;
                background: #007cba;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
                cursor: pointer;
            `;

            toggleButton.onclick = () => {
                debugPanel.style.display = debugPanel.style.display === 'none' ? 'block' : 'none';
                updateDebugInfo();
            };

            function updateDebugInfo() {
                debugPanel.innerHTML = `
                    <strong>Debug Info:</strong><br>
                    Screen: ${screen.width}x${screen.height}<br>
                    Viewport: ${window.innerWidth}x${window.innerHeight}<br>
                    DPR: ${window.devicePixelRatio}<br>
                    UA: ${navigator.userAgent.substring(0, 50)}...<br>
                    Connection: ${navigator.connection?.effectiveType || 'Unknown'}<br>
                    Memory: ${navigator.deviceMemory || 'Unknown'}GB<br>
                    Cores: ${navigator.hardwareConcurrency || 'Unknown'}<br>
                    Touch: ${('ontouchstart' in window) ? 'Yes' : 'No'}
                `;
            }

            document.body.appendChild(debugPanel);
            document.body.appendChild(toggleButton);
        }
    }

    // Initialize all cross-device features
    const deviceInfo = initDeviceDetection();
    initOrientationHandling();
    initCompatibilityFixes();
    initPerformanceTesting();
    initDebugUtilities();

    // Global error handling for better debugging
    window.addEventListener('error', (e) => {
        console.error('Global error:', e.error);
        // Could send to error tracking service
    });

    window.addEventListener('unhandledrejection', (e) => {
        console.error('Unhandled promise rejection:', e.reason);
        // Could send to error tracking service
    });

    // Enhanced Section Interactions

    // Product Filter Functionality
    function initProductFilters() {
        const filterTabs = document.querySelectorAll('.filter-tab');
        const productCards = document.querySelectorAll('.enhanced-product-card');

        filterTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const filter = this.dataset.filter;

                // Update active tab
                filterTabs.forEach(t => t.classList.remove('active'));
                this.classList.add('active');

                // Filter products
                productCards.forEach(card => {
                    const category = card.dataset.category;
                    if (filter === 'all' || category === filter) {
                        card.style.display = 'block';
                        card.style.animation = 'fadeInUp 0.5s ease-out';
                    } else {
                        card.style.display = 'none';
                    }
                });

                // Track filter usage
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'product_filter', {
                        'event_category': 'engagement',
                        'event_label': filter,
                        'value': 1
                    });
                }
            });
        });
    }

    // Enhanced Add to Cart Functionality
    function initEnhancedCart() {
        const addToCartBtns = document.querySelectorAll('.btn-add-to-cart');

        addToCartBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const productId = this.dataset.productId;
                const quantity = this.dataset.quantity || 1;

                // Add loading state
                this.classList.add('loading');
                this.disabled = true;

                // Simulate API call
                setTimeout(() => {
                    // Remove loading state
                    this.classList.remove('loading');
                    this.disabled = false;

                    // Show success feedback
                    this.innerHTML = '<div class="btn-content"><i class="fas fa-check"></i><span>Added!</span></div>';

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        this.innerHTML = '<div class="btn-content"><i class="fas fa-shopping-cart"></i><span>Add to Cart</span></div>';
                    }, 2000);

                    // Update cart count
                    updateCartCount();

                    // Show notification
                    showNotification('Product added to cart!', 'success');

                    // Track conversion
                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'add_to_cart', {
                            'event_category': 'ecommerce',
                            'event_label': productId,
                            'value': 1
                        });
                    }
                }, 1000);
            });
        });
    }

    // Newsletter Subscription
    function initNewsletterForm() {
        const form = document.getElementById('newsletterForm');
        const successDiv = document.getElementById('newsletterSuccess');

        if (form) {
            form.addEventListener('submit', function(e) {
                e.preventDefault();

                const email = document.getElementById('newsletterEmail').value;
                const submitBtn = form.querySelector('.newsletter-btn');

                // Add loading state
                submitBtn.classList.add('loading');
                submitBtn.disabled = true;

                // Simulate API call
                setTimeout(() => {
                    // Hide form and show success
                    form.style.display = 'none';
                    successDiv.style.display = 'block';
                    successDiv.style.animation = 'fadeInUp 0.5s ease-out';

                    // Track subscription
                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'newsletter_signup', {
                            'event_category': 'engagement',
                            'event_label': 'footer_newsletter',
                            'value': 1
                        });
                    }

                    // Show notification
                    showNotification('Successfully subscribed to newsletter!', 'success');
                }, 1500);
            });
        }
    }

    // Quick Actions (Wishlist, Compare, Quick View)
    function initQuickActions() {
        // Wishlist functionality
        const wishlistBtns = document.querySelectorAll('.add-to-wishlist');
        wishlistBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const productId = this.dataset.productId;
                const icon = this.querySelector('i');

                if (icon.classList.contains('far')) {
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                    this.style.color = '#e91e63';
                    showNotification('Added to wishlist!', 'success');
                } else {
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                    this.style.color = '';
                    showNotification('Removed from wishlist!', 'info');
                }

                // Track wishlist action
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'wishlist_toggle', {
                        'event_category': 'engagement',
                        'event_label': productId,
                        'value': 1
                    });
                }
            });
        });

        // Quick view functionality
        const quickViewBtns = document.querySelectorAll('.quick-view');
        quickViewBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const productId = this.dataset.productId;
                showQuickViewModal(productId);
            });
        });

        // Compare functionality
        const compareBtns = document.querySelectorAll('.add-to-compare');
        compareBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const productId = this.dataset.productId;
                showNotification('Added to comparison list!', 'info');

                // Track compare action
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'add_to_compare', {
                        'event_category': 'engagement',
                        'event_label': productId,
                        'value': 1
                    });
                }
            });
        });
    }

    // Utility Functions
    function updateCartCount() {
        const cartBadge = document.querySelector('.cart-badge');
        if (cartBadge) {
            let currentCount = parseInt(cartBadge.textContent) || 0;
            currentCount++;
            cartBadge.textContent = currentCount;
            cartBadge.style.animation = 'pulse 0.5s ease-in-out';
            setTimeout(() => {
                cartBadge.style.animation = '';
            }, 500);
        }
    }

    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 10000;
            animation: slideInFromRight 0.3s ease-out;
            max-width: 300px;
        `;

        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutToRight 0.3s ease-in';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }

    function showQuickViewModal(productId) {
        // Create modal overlay
        const modal = document.createElement('div');
        modal.className = 'quick-view-modal';
        modal.innerHTML = `
            <div class="modal-overlay"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Quick View</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Loading product details...</p>
                    </div>
                </div>
            </div>
        `;

        // Add modal styles
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        document.body.appendChild(modal);

        // Close modal functionality
        const closeBtn = modal.querySelector('.modal-close');
        const overlay = modal.querySelector('.modal-overlay');

        [closeBtn, overlay].forEach(element => {
            element.addEventListener('click', () => {
                modal.remove();
            });
        });

        // Simulate loading product data
        setTimeout(() => {
            modal.remove();
            showNotification('Quick view feature coming soon!', 'info');
        }, 2000);
    }

    // Initialize all enhanced functionality
    initProductFilters();
    initEnhancedCart();
    initNewsletterForm();
    initQuickActions();

    // Add notification styles to head
    const notificationStyles = document.createElement('style');
    notificationStyles.textContent = `
        @keyframes slideInFromRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutToRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .quick-view-modal .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
        }

        .quick-view-modal .modal-content {
            background: var(--dark-card);
            border-radius: 12px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            position: relative;
            z-index: 1;
        }

        .quick-view-modal .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .quick-view-modal .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--text-secondary);
            cursor: pointer;
        }

        .loading-spinner {
            text-align: center;
            padding: 2rem;
            color: var(--text-secondary);
        }

        .loading-spinner i {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: var(--primary-purple);
        }
    `;
    document.head.appendChild(notificationStyles);

    console.log('✅ Cleanance Lab enhanced landing page complete!');
    console.log('📱 Device compatibility initialized');
    console.log('🚀 Performance optimizations active');
    console.log('🎯 Enhanced sections loaded: Categories, Products, Services, About, Testimonials, Trust, Newsletter');
</script>