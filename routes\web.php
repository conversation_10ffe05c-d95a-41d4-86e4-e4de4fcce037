<?php

/**
 * Web Routes for Cleanance Lab E-commerce
 * This file defines all the URL routes for the application
 */

return [
    // Home routes
    'GET|/' => 'HomeController@index',
    'GET|/home' => 'HomeController@index',
    'GET|/about' => 'HomeController@about',
    'GET|/contact' => 'HomeController@contact',

    // Product routes
    'GET|/products' => 'HomeController@products',
    'GET|/product/([0-9]+)' => 'HomeController@productDetail',
    'GET|/category/([0-9]+)' => 'HomeController@categoryProducts',
    'POST|/search' => 'HomeController@search',
    'GET|/search' => 'HomeController@search',

    // Authentication routes
    'GET|/login' => 'AuthController@login',
    'POST|/login' => 'AuthController@login',
    'GET|/register' => 'AuthController@register',
    'POST|/register' => 'AuthController@register',
    'GET|/logout' => 'Auth<PERSON>ontroller@logout',
    'GET|/verify/([a-zA-Z0-9]+)' => 'AuthController@verify',
    'GET|/forgot-password' => 'AuthController@forgotPassword',
    'POST|/forgot-password' => 'AuthController@forgotPassword',
    'GET|/reset-password/([a-zA-Z0-9]+)' => 'AuthController@resetPassword',
    'POST|/reset-password/([a-zA-Z0-9]+)' => 'AuthController@resetPassword',
    'GET|/profile' => 'AuthController@profile',
    'POST|/profile' => 'AuthController@updateProfile',
    'GET|/change-password' => 'AuthController@changePassword',
    'POST|/change-password' => 'AuthController@changePassword',

    // Cart routes
    'GET|/cart' => 'CartController@index',
    'POST|/cart/add' => 'CartController@addItem',
    'POST|/cart/update' => 'CartController@updateItem',
    'POST|/cart/remove' => 'CartController@removeItem',
    'POST|/cart/clear' => 'CartController@clearCart',
    'POST|/cart/apply-discount' => 'CartController@applyDiscount',
    'POST|/cart/remove-discount' => 'CartController@removeDiscount',
    'GET|/checkout' => 'CartController@checkout',
    'POST|/checkout' => 'CartController@processCheckout',
    'GET|/order/([0-9]+)' => 'CartController@orderDetail',
    'GET|/orders' => 'CartController@orderHistory',
    'POST|/order/cancel/([0-9]+)' => 'CartController@cancelOrder',

    // Wishlist routes
    'GET|/wishlist' => 'HomeController@wishlist',
    'POST|/wishlist/add' => 'HomeController@addToWishlist',
    'POST|/wishlist/remove' => 'HomeController@removeFromWishlist',
    'POST|/wishlist/clear' => 'HomeController@clearWishlist',

    // Review routes
    'POST|/review/add' => 'HomeController@addReview',
    'POST|/review/update' => 'HomeController@updateReview',
    'POST|/review/delete' => 'HomeController@deleteReview',

    // Admin routes
    'GET|/admin' => 'AdminController@dashboard',
    'GET|/admin/dashboard' => 'AdminController@dashboard',
    'GET|/admin/analytics' => 'AdminController@analytics',
    'GET|/admin/analytics/export' => 'AdminController@exportAnalytics',

    // Admin Products
    'GET|/admin/products' => 'AdminController@products',
    'GET|/admin/products/add' => 'AdminController@addProduct',
    'POST|/admin/products/add' => 'AdminController@addProduct',
    'GET|/admin/products/edit/([0-9]+)' => 'AdminController@editProduct',
    'POST|/admin/products/edit/([0-9]+)' => 'AdminController@editProduct',
    'POST|/admin/products/delete/([0-9]+)' => 'AdminController@deleteProduct',
    'POST|/admin/products/([0-9]+)/toggle-status' => 'AdminController@toggleProductStatus',

    // Admin Orders
    'GET|/admin/orders' => 'AdminController@orders',
    'GET|/admin/orders/([0-9]+)' => 'AdminController@orderDetail',
    'POST|/admin/orders/([0-9]+)/status' => 'AdminController@updateOrderStatus',
    'POST|/admin/orders/([0-9]+)/cancel' => 'AdminController@cancelOrder',
    'GET|/admin/orders/export' => 'AdminController@exportOrders',

    // Admin Users
    'GET|/admin/users' => 'AdminController@users',
    'GET|/admin/users/([0-9]+)' => 'AdminController@userDetail',
    'POST|/admin/users/([0-9]+)/toggle-status' => 'AdminController@toggleUserStatus',
    'POST|/admin/users/([0-9]+)/toggle-role' => 'AdminController@toggleUserRole',
    'POST|/admin/users/([0-9]+)/delete' => 'AdminController@deleteUser',
    'GET|/admin/users/export' => 'AdminController@exportUsers',

    // Admin Categories
    'GET|/admin/categories' => 'AdminController@categories',
    'GET|/admin/categories/add' => 'AdminController@addCategory',
    'POST|/admin/categories/add' => 'AdminController@addCategory',
    'GET|/admin/categories/edit/([0-9]+)' => 'AdminController@editCategory',
    'POST|/admin/categories/edit/([0-9]+)' => 'AdminController@editCategory',
    'POST|/admin/categories/delete/([0-9]+)' => 'AdminController@deleteCategory',
    'POST|/admin/categories/([0-9]+)/toggle-status' => 'AdminController@toggleCategoryStatus',

    // Admin Discount Codes
    'GET|/admin/discount-codes' => 'AdminController@discountCodes',
    'GET|/admin/discount-codes/add' => 'AdminController@addDiscountCode',
    'POST|/admin/discount-codes/add' => 'AdminController@addDiscountCode',
    'GET|/admin/discount-codes/edit/([0-9]+)' => 'AdminController@editDiscountCode',
    'POST|/admin/discount-codes/edit/([0-9]+)' => 'AdminController@editDiscountCode',
    'POST|/admin/discount-codes/delete/([0-9]+)' => 'AdminController@deleteDiscountCode',

    // Admin Settings
    'GET|/admin/settings' => 'AdminController@settings',
    'POST|/admin/settings' => 'AdminController@settings',

    // Admin Reports
    'GET|/admin/reports/sales' => 'AdminController@salesReport',
    'GET|/admin/reports/products' => 'AdminController@productsReport',
    'GET|/admin/reports/users' => 'AdminController@usersReport',

    // API routes for AJAX requests
    'POST|/api/cart/add' => 'CartController@addItem',
    'POST|/api/cart/update' => 'CartController@updateItem',
    'POST|/api/cart/remove' => 'CartController@removeItem',
    'POST|/api/wishlist/add' => 'HomeController@addToWishlist',
    'POST|/api/wishlist/remove' => 'HomeController@removeFromWishlist',
    'POST|/api/review/add' => 'HomeController@addReview',
    'GET|/api/products/search' => 'HomeController@search',
    'GET|/api/categories' => 'HomeController@getCategories',

    // Error routes
    'GET|/404' => 'ErrorController@notFound',
    'GET|/500' => 'ErrorController@serverError',
    'GET|/401' => 'ErrorController@unauthorized',
    'GET|/403' => 'ErrorController@forbidden',

    // Additional user routes
    'GET|/account' => 'AuthController@account',
    'GET|/account/orders' => 'CartController@orderHistory',
    'GET|/account/addresses' => 'AuthController@addresses',
    'POST|/account/addresses/add' => 'AuthController@addAddress',
    'POST|/account/addresses/edit/([0-9]+)' => 'AuthController@editAddress',
    'POST|/account/addresses/delete/([0-9]+)' => 'AuthController@deleteAddress',
    'GET|/account/settings' => 'AuthController@settings',
    'POST|/account/settings' => 'AuthController@updateSettings',

    // Newsletter subscription
    'POST|/newsletter/subscribe' => 'HomeController@subscribeNewsletter',
    'POST|/newsletter/unsubscribe' => 'HomeController@unsubscribeNewsletter',

    // Contact and support
    'POST|/contact' => 'HomeController@sendContact',
    'GET|/support' => 'HomeController@support',
    'POST|/support' => 'HomeController@sendSupport',

    // Legal pages
    'GET|/privacy-policy' => 'HomeController@privacyPolicy',
    'GET|/terms-of-service' => 'HomeController@termsOfService',
    'GET|/shipping-policy' => 'HomeController@shippingPolicy',
    'GET|/return-policy' => 'HomeController@returnPolicy',

    // New pages
    'GET|/help-center' => 'HomeController@helpCenter',
    'GET|/shipping-info' => 'HomeController@shippingInfo',
    'GET|/returns' => 'HomeController@returns',

    // Sitemap and SEO
    'GET|/sitemap.xml' => 'HomeController@sitemap',
    'GET|/robots.txt' => 'HomeController@robots',

    // Payment webhooks (for payment providers)
    'POST|/webhook/stripe' => 'CartController@stripeWebhook',
    'POST|/webhook/paypal' => 'CartController@paypalWebhook',

    // Email verification and password reset
    'GET|/email/verify/([a-zA-Z0-9]+)' => 'AuthController@verifyEmail',
    'POST|/email/resend' => 'AuthController@resendVerification',

    // Admin Contact Requests
    'GET|/admin/contact-requests' => 'AdminController@contactRequests',
    'GET|/admin/contact-requests/view/([0-9]+)' => 'AdminController@viewContactRequest',
    'POST|/admin/contact-requests/archive/([0-9]+)' => 'AdminController@archiveContactRequest',
    'POST|/admin/contact-requests/delete/([0-9]+)' => 'AdminController@deleteContactRequest',
];
