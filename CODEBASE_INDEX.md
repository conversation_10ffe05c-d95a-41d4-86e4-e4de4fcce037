# Cleanance Lab E-commerce - Codebase Index

## Project Overview

**Cleanance Lab** is a modern PHP-based e-commerce platform for cosmetic/lab products with a dark theme UI. The application follows an MVC architecture pattern with custom routing and database abstraction.

## Technology Stack

- **Backend**: PHP 8.0+ with PDO
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Database**: MySQL
- **Styling**: Custom CSS with Google Fonts & Font Awesome
- **Email**: PHPMailer with OVH SMTP
- **Architecture**: Custom MVC Framework

## Project Structure

```
cleanance-lab/
├── app/
│   ├── controllers/     # Application controllers
│   ├── models/         # Database models
│   ├── views/          # View templates
│   └── helpers/        # Utility classes
├── config/             # Configuration files
├── database/           # Database schema and migrations
├── public/             # Web-accessible files
│   ├── assets/         # CSS, JS, images
│   ├── uploads/        # User uploaded files
│   └── index.php       # Application entry point
├── routes/             # Route definitions
└── vendor/             # Composer dependencies
```

## Core Configuration

### Application Settings (`config/app.php`)

- **App Name**: Cleanance Lab
- **Version**: 1.0.0
- **Timezone**: Africa/Tunis
- **Debug Mode**: Enabled
- **Session Lifetime**: 1 hour
- **Upload Limits**: 5MB max file size
- **Pagination**: 12 items per page

### Database Configuration (`config/database.php`)

- **Host**: localhost
- **Database**: cleanance_lab
- **Username**: root
- **Password**: (empty)
- **Charset**: utf8mb4

## Database Schema

### Core Tables

1. **users** - User accounts and profiles
2. **categories** - Product categories with hierarchy
3. **products** - Product catalog with pricing and inventory
4. **product_images** - Product image gallery
5. **product_reviews** - Customer reviews and ratings
6. **orders** - Order management and tracking
7. **order_items** - Individual order line items
8. **cart_items** - Shopping cart persistence
9. **wishlist_items** - User wishlists
10. **discount_codes** - Promotional codes
11. **contact_requests** - Customer inquiries
12. **email_templates** - Email template management
13. **settings** - Application settings

## MVC Architecture

### Controllers (`app/controllers/`)

#### BaseController.php

- **Purpose**: Base class for all controllers
- **Key Methods**:
  - `render($view, $data)` - Render views with layout
  - `getCurrentUser()` - Get authenticated user
  - `requireAuth()` - Enforce authentication
  - `redirect($url)` - Handle redirects
  - `json($data)` - JSON responses

#### HomeController.php

- **Purpose**: Public-facing pages and product display
- **Key Methods**:
  - `index()` - Homepage with featured products
  - `products()` - Product listing with filters
  - `productDetail($id)` - Individual product pages
  - `search()` - Product search functionality
  - `addToWishlist()` - Wishlist management

#### AuthController.php

- **Purpose**: User authentication and account management
- **Key Methods**:
  - `login()` - User login
  - `register()` - User registration
  - `logout()` - User logout
  - `forgotPassword()` - Password reset
  - `profile()` - User profile management

#### AdminController.php

- **Purpose**: Administrative interface
- **Key Methods**:
  - `dashboard()` - Admin dashboard with analytics
  - `products()` - Product management
  - `orders()` - Order management
  - `users()` - User management
  - `analytics()` - Sales and performance reports

#### CartController.php

- **Purpose**: Shopping cart functionality
- **Key Methods**:
  - `index()` - Cart display
  - `addItem()` - Add products to cart
  - `updateItem()` - Update cart quantities
  - `removeItem()` - Remove cart items
  - `checkout()` - Checkout process

### Models (`app/models/`)

#### User.php

- **Purpose**: User account operations
- **Key Methods**:
  - `create($data)` - Create new user
  - `findByEmail($email)` - Find user by email
  - `update($id, $data)` - Update user profile
  - `updatePassword($id, $password)` - Change password
  - `verifyEmail($token)` - Email verification

#### Product.php

- **Purpose**: Product catalog management
- **Key Methods**:
  - `getAll($filters, $limit, $offset)` - Product listing with filters
  - `getFeatured($limit)` - Featured products
  - `findById($id)` - Get product details
  - `search($query, $limit)` - Product search
  - `updateStock($id, $quantity)` - Inventory management

#### Category.php

- **Purpose**: Product category management
- **Key Methods**:
  - `getAll()` - All categories
  - `findById($id)` - Category by ID
  - `getAllWithCount()` - Categories with product counts

#### Cart.php

- **Purpose**: Shopping cart operations
- **Key Methods**:
  - `getItems($userId)` - Get cart items
  - `addItem($userId, $productId, $quantity)` - Add to cart
  - `updateItem($userId, $productId, $quantity)` - Update quantities
  - `removeItem($userId, $productId)` - Remove from cart

### Helpers (`app/helpers/`)

#### UrlHelper.php

- **Purpose**: URL generation and routing helpers
- **Key Methods**:
  - `url($path)` - Generate application URLs
  - `asset($path)` - Asset URL generation
  - `css($filename)` - CSS file URLs
  - `js($filename)` - JavaScript file URLs
  - `image($filename)` - Image URLs

## Routing System (`routes/web.php`)

### Route Categories

- **Home Routes**: `/`, `/home`, `/about`, `/contact`
- **Product Routes**: `/products`, `/product/{id}`, `/category/{id}`
- **Auth Routes**: `/login`, `/register`, `/logout`, `/profile`
- **Cart Routes**: `/cart`, `/checkout`
- **Admin Routes**: `/admin/*` (dashboard, products, orders, users)
- **API Routes**: `/api/*` (AJAX endpoints)

## View Structure (`app/views/`)

### Layout System

- **Main Layout**: `layouts/main.php` - Base template with header/footer
- **Dark Theme**: Modern dark UI with purple accent (#7E57C2)

### View Categories

- **home/**: Homepage and public pages
- **auth/**: Login, registration, profile pages
- **products/**: Product listing and detail pages
- **cart/**: Shopping cart and checkout
- **admin/**: Administrative interface
- **errors/**: Error pages (404, 500, etc.)

## Security Features

- **CSRF Protection**: Token-based CSRF prevention
- **Password Hashing**: PHP password_hash() with bcrypt
- **SQL Injection Prevention**: PDO prepared statements
- **Session Security**: Secure session configuration
- **Input Validation**: Server-side validation
- **File Upload Security**: Type and size restrictions

## Key Features Implemented

✅ User authentication and authorization
✅ Product catalog with categories
✅ Shopping cart and checkout
✅ Order management system
✅ Admin dashboard with analytics
✅ Product reviews and ratings
✅ Wishlist functionality
✅ Discount code system
✅ Email notifications
✅ Responsive dark theme UI
✅ Search and filtering
✅ File upload handling

## API Endpoints (`/api/*`)

### Cart API

- `POST /api/cart/add` - Add item to cart
- `POST /api/cart/update` - Update cart item quantity
- `POST /api/cart/remove` - Remove item from cart

### Wishlist API

- `POST /api/wishlist/add` - Add to wishlist
- `POST /api/wishlist/remove` - Remove from wishlist

### Product API

- `GET /api/products/search` - Search products
- `GET /api/categories` - Get categories list

### Review API

- `POST /api/review/add` - Add product review

## Email Configuration

- **SMTP Host**: ssl0.ovh.net
- **SMTP Port**: 465 (SSL)
- **Username**: <EMAIL>
- **Encryption**: SSL

## Payment Integration (Demo)

- **Stripe**: Test keys configured
- **PayPal**: Client ID/Secret placeholders

## File Upload System

- **Upload Path**: `public/uploads/`
- **Max Size**: 5MB
- **Allowed Types**: jpg, jpeg, png, gif, webp
- **Categories**: products/, users/

## Admin Features

- **Dashboard Analytics**: Sales metrics, top products
- **Product Management**: CRUD operations, image upload
- **Order Management**: Status updates, tracking
- **User Management**: Account administration
- **Reports**: Sales, products, users reports
- **Settings**: Application configuration

## Development Notes

- **Debug Mode**: Currently enabled for development
- **Error Reporting**: Full error reporting enabled
- **Session Management**: Custom session handling
- **Database**: Uses PDO with prepared statements
- **Autoloading**: Custom SPL autoloader for classes
- **Composer**: PHPMailer dependency management

## Installation Files

- `install.php` - Database setup and configuration
- `database/schema.sql` - Complete database schema
- `config/installed.txt` - Installation status marker

## Testing Files

- `public/test_discount.html` - Discount code testing
- `public/test_price_update.html` - Price update testing
