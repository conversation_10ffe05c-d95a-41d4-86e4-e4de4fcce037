# Cleanance Lab - Detailed File Structure Index

## Root Directory Files
```
├── COMPLETION_SUMMARY.md      # Project completion status and features
├── README.md                  # Project documentation and setup guide
├── composer.json              # Composer dependencies (PHPMailer)
├── composer.lock              # Composer lock file
├── install.php                # Database installation script
├── bash.exe.stackdump         # System file (can be ignored)
└── CODEBASE_INDEX.md          # This codebase documentation
```

## Configuration (`/config/`)
```
├── app.php                    # Main application configuration
│   ├── App settings (name, version, URL)
│   ├── Security settings (CSRF, session)
│   ├── Upload settings (file size, types)
│   ├── Email/SMTP configuration
│   ├── Payment gateway settings
│   └── File paths and debug settings
├── database.php               # Database connection configuration
└── installed.txt              # Installation completion marker
```

## Database (`/database/`)
```
└── schema.sql                 # Complete MySQL database schema
    ├── users table
    ├── categories table
    ├── products table
    ├── product_images table
    ├── product_reviews table
    ├── orders table
    ├── order_items table
    ├── cart_items table
    ├── wishlist_items table
    ├── discount_codes table
    ├── contact_requests table
    ├── email_templates table
    └── settings table
```

## Application (`/app/`)

### Controllers (`/app/controllers/`)
```
├── BaseController.php         # Base controller with common functionality
├── HomeController.php         # Public pages and product display
├── AuthController.php         # User authentication and accounts
├── CartController.php         # Shopping cart functionality
├── AdminController.php        # Administrative interface
├── AdminController_backup.php # Backup of admin controller
├── AdminController_temp.php   # Temporary admin controller version
└── ErrorController.php        # Error handling
```

### Models (`/app/models/`)
```
├── User.php                   # User account operations
├── Product.php                # Product catalog management
├── Category.php               # Product category operations
└── Cart.php                   # Shopping cart operations
```

### Helpers (`/app/helpers/`)
```
└── UrlHelper.php              # URL generation and routing utilities
```

### Views (`/app/views/`)

#### Layouts (`/app/views/layouts/`)
```
└── main.php                   # Main layout template with header/footer
```

#### Home Views (`/app/views/home/<USER>
```
└── index.php                  # Homepage template
```

#### Authentication Views (`/app/views/auth/`)
```
├── login.php                  # Login form
├── register.php               # Registration form
├── forgot-password.php        # Password reset request
├── reset-password.php         # Password reset form
├── verify.php                 # Email verification
├── resend-verification.php    # Resend verification email
├── profile.php                # User profile page
├── account.php                # Account overview
├── addresses.php              # Address management
├── edit-address.php           # Address editing form
├── change-password.php        # Password change form
└── settings.php               # Account settings
```

#### Product Views (`/app/views/products/`)
```
├── index.php                  # Product listing page
├── detail.php                 # Product detail page
├── _grid.php                  # Product grid partial
└── _sidebar.php               # Product filter sidebar partial
```

#### Cart Views (`/app/views/cart/`)
```
├── index.php                  # Shopping cart page
└── checkout.php               # Checkout process
```

#### Order Views (`/app/views/orders/`)
```
├── history.php                # Order history
└── detail.php                 # Order detail page
```

#### Wishlist Views (`/app/views/wishlist/`)
```
└── index.php                  # Wishlist page
```

#### Admin Views (`/app/views/admin/`)
```
├── dashboard.php              # Admin dashboard
├── products.php               # Product management
├── add-product.php            # Add new product
├── edit-product.php           # Edit product
├── categories.php             # Category management
├── add-category.php           # Add new category
├── edit-category.php          # Edit category
├── orders.php                 # Order management
├── order-detail.php           # Order detail view
├── users.php                  # User management
├── user-detail.php            # User detail view
├── discount-codes.php         # Discount code management
├── add-discount-code.php      # Add discount code
├── edit-discount-code.php     # Edit discount code
├── contact-requests.php       # Contact form submissions
├── contact-request-view.php   # View contact request
├── analytics.php              # Analytics dashboard
├── sales-report.php           # Sales reports
├── products-report.php        # Product reports
├── users-report.php           # User reports
└── settings.php               # Admin settings
```

#### Static Pages (`/app/views/pages/`)
```
├── about.php                  # About page
├── contact.php                # Contact form
├── help-center.php            # Help center
├── privacy-policy.php         # Privacy policy
├── terms-of-service.php       # Terms of service
├── shipping-policy.php        # Shipping policy
├── shipping-info.php          # Shipping information
├── returns.php                # Returns policy
├── support.php                # Support page
├── 404.php                    # 404 error page
└── 500.php                    # 500 error page
```

#### Error Views (`/app/views/errors/`)
```
├── 401.php                    # Unauthorized error
├── 403.php                    # Forbidden error
├── 404.php                    # Not found error
└── 500.php                    # Internal server error
```

## Public Directory (`/public/`)

### Entry Point
```
└── index.php                  # Application entry point and router
```

### Assets (`/public/assets/`)
```
├── css/                       # Stylesheets
├── js/                        # JavaScript files
└── img/                       # Images and graphics
```

### Uploads (`/public/uploads/`)
```
├── products/                  # Product images
└── users/                     # User uploaded files
```

### Test Files
```
├── test_discount.html         # Discount code testing
└── test_price_update.html     # Price update testing
```

## Routes (`/routes/`)
```
└── web.php                    # Application route definitions
    ├── Home routes (/, /home, /about, /contact)
    ├── Product routes (/products, /product/{id})
    ├── Authentication routes (/login, /register, /logout)
    ├── Cart routes (/cart, /checkout)
    ├── Admin routes (/admin/*)
    └── API routes (/api/*)
```

## Vendor Directory (`/vendor/`)
```
├── autoload.php               # Composer autoloader
├── composer/                  # Composer files
└── phpmailer/                 # PHPMailer library
```

## Key File Purposes

### Configuration Files
- **config/app.php**: Central configuration for the entire application
- **config/database.php**: Database connection settings
- **routes/web.php**: URL routing definitions

### Core Application Files
- **public/index.php**: Application bootstrap and request handling
- **app/controllers/BaseController.php**: Common controller functionality
- **app/views/layouts/main.php**: Main HTML template structure

### Database Files
- **database/schema.sql**: Complete database structure
- **install.php**: Automated database setup

### Documentation Files
- **README.md**: Project overview and setup instructions
- **COMPLETION_SUMMARY.md**: Feature completion status
- **CODEBASE_INDEX.md**: Comprehensive codebase documentation
