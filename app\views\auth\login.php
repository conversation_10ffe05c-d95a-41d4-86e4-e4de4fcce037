<div class="auth-container">
    <div class="container">
        <div class="auth-content">
            <div class="auth-card">
                <div class="auth-header">
                    <div class="auth-logo">
                        <i class="fas fa-flask"></i>
                        <span>Cleanance Lab</span>
                    </div>
                    <h1 class="auth-title">Welcome Back</h1>
                    <p class="auth-subtitle">Sign in to your account to continue shopping</p>
                </div>

                <?php if (isset($error)): ?>
                    <script>
                        window.addEventListener('DOMContentLoaded', function() {
                            showNotification('<?= addslashes(htmlspecialchars($error)) ?>', 'error');
                        });
                    </script>
                <?php endif; ?>

                <form class="auth-form" method="POST" action="<?= UrlHelper::url('/login') ?>">
                    <input type="hidden" name="csrf_token" value="<?= $this->generateCSRFToken() ?>">

                    <div class="form-group">
                        <label for="email" class="form-label">Email Address</label>
                        <div class="input-group">
                            <i class="fas fa-envelope input-icon"></i>
                            <input type="email" id="email" name="email"
                                value="<?= htmlspecialchars($email ?? '') ?>"
                                class="form-input" required
                                placeholder="Enter your email">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <i class="fas fa-lock input-icon"></i>
                            <input type="password" id="password" name="password"
                                class="form-input" required
                                placeholder="Enter your password">
                            <button type="button" class="password-toggle" onclick="togglePassword()">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-label">
                            <input type="checkbox" name="remember" value="1">
                            <span class="checkmark"></span>
                            Remember me
                        </label>
                        <a href="<?= UrlHelper::url('/forgot-password') ?>" class="forgot-link">
                            Forgot password?
                        </a>
                    </div>

                    <button type="submit" class="btn btn-primary btn-lg auth-submit">
                        <i class="fas fa-sign-in-alt"></i>
                        Sign In
                    </button>
                </form>

                <div class="auth-divider">
                    <span>or</span>
                </div>

                <div class="social-auth">
                    <button class="btn btn-outline social-btn" onclick="socialLogin('google')">
                        <i class="fab fa-google"></i>
                        Continue with Google
                    </button>
                    <button class="btn btn-outline social-btn" onclick="socialLogin('facebook')">
                        <i class="fab fa-facebook-f"></i>
                        Continue with Facebook
                    </button>
                </div>

                <div class="auth-footer">
                    <p>Don't have an account?
                        <a href="<?= UrlHelper::url('/register') ?>" class="auth-link">Sign up</a>
                    </p>
                </div>
            </div>

            <div class="auth-image">
                <div class="auth-visual">
                    <div class="floating-shapes">
                        <div class="shape shape-1"></div>
                        <div class="shape shape-2"></div>
                        <div class="shape shape-3"></div>
                    </div>
                    <div class="auth-illustration">
                        <i class="fas fa-user-circle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .auth-container {
        min-height: 100vh;
        background: linear-gradient(135deg, var(--dark-card) 0%, var(--dark-surface) 100%);
        display: flex;
        align-items: center;
        padding: var(--spacing-xl) 0;
    }

    .auth-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-2xl);
        align-items: center;
        max-width: 1000px;
        margin: 0 auto;
    }

    .auth-card {
        background-color: var(--dark-card);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-xl);
        padding: var(--spacing-2xl);
        box-shadow: var(--shadow-xl);
    }

    .auth-header {
        text-align: center;
        margin-bottom: var(--spacing-xl);
    }

    .auth-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        font-size: var(--font-size-xl);
        font-weight: 700;
        color: var(--primary-purple);
        margin-bottom: var(--spacing-lg);
    }

    .auth-logo i {
        font-size: var(--font-size-2xl);
    }

    .auth-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
    }

    .auth-subtitle {
        color: var(--text-secondary);
        font-size: var(--font-size-base);
    }

    .auth-form {
        margin-bottom: var(--spacing-xl);
    }

    .form-group {
        margin-bottom: var(--spacing-lg);
    }

    .form-label {
        display: block;
        margin-bottom: var(--spacing-sm);
        font-weight: 500;
        color: var(--text-primary);
    }

    .input-group {
        position: relative;
        display: flex;
        align-items: center;
    }

    .input-icon {
        position: absolute;
        left: var(--spacing-md);
        color: var(--text-secondary);
        z-index: 1;
    }

    .form-input {
        width: 100%;
        padding: var(--spacing-md);
        padding-left: 3rem;
        background-color: var(--dark-surface);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        color: var(--text-primary);
        font-size: var(--font-size-base);
        transition: all var(--transition-fast);
    }

    .form-input:focus {
        outline: none;
        border-color: var(--primary-purple);
        box-shadow: 0 0 0 3px rgba(126, 87, 194, 0.1);
    }

    .password-toggle {
        position: absolute;
        right: var(--spacing-md);
        background: none;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        padding: var(--spacing-xs);
        transition: color var(--transition-fast);
    }

    .password-toggle:hover {
        color: var(--primary-purple);
    }

    .form-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-lg);
    }

    .checkbox-label {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        cursor: pointer;
        font-size: var(--font-size-sm);
        color: var(--text-primary);
    }

    .checkbox-label input[type="checkbox"] {
        display: none;
    }

    .checkmark {
        width: 18px;
        height: 18px;
        border: 2px solid var(--border-color);
        border-radius: var(--radius-sm);
        position: relative;
        transition: all var(--transition-fast);
    }

    .checkbox-label input[type="checkbox"]:checked+.checkmark {
        background-color: var(--primary-purple);
        border-color: var(--primary-purple);
    }

    .checkbox-label input[type="checkbox"]:checked+.checkmark::after {
        content: '✓';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: var(--white);
        font-size: var(--font-size-xs);
        font-weight: bold;
    }

    .forgot-link {
        color: var(--primary-purple);
        text-decoration: none;
        font-size: var(--font-size-sm);
        transition: color var(--transition-fast);
    }

    .forgot-link:hover {
        color: var(--primary-purple-dark);
        text-decoration: underline;
    }

    .auth-submit {
        width: 100%;
        margin-bottom: var(--spacing-lg);
    }

    .auth-divider {
        text-align: center;
        margin: var(--spacing-xl) 0;
        position: relative;
    }

    .auth-divider::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background-color: var(--border-color);
    }

    .auth-divider span {
        background-color: var(--dark-card);
        padding: 0 var(--spacing-md);
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }

    .social-auth {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-xl);
    }

    .social-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
        font-size: var(--font-size-base);
    }

    .social-btn i {
        font-size: var(--font-size-lg);
    }

    .auth-footer {
        text-align: center;
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
    }

    .auth-link {
        color: var(--primary-purple);
        text-decoration: none;
        font-weight: 500;
        transition: color var(--transition-fast);
    }

    .auth-link:hover {
        color: var(--primary-purple-dark);
        text-decoration: underline;
    }

    .auth-image {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .auth-visual {
        position: relative;
        width: 400px;
        height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .auth-illustration {
        font-size: 120px;
        color: var(--primary-purple);
        opacity: 0.8;
        animation: float 6s ease-in-out infinite;
    }

    .floating-shapes {
        position: absolute;
        width: 100%;
        height: 100%;
    }

    .shape {
        position: absolute;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-purple) 0%, var(--primary-purple-light) 100%);
        opacity: 0.3;
        animation: float 4s ease-in-out infinite;
    }

    .shape-1 {
        width: 60px;
        height: 60px;
        top: 20%;
        left: 20%;
        animation-delay: 0s;
    }

    .shape-2 {
        width: 40px;
        height: 40px;
        top: 60%;
        right: 20%;
        animation-delay: 1s;
    }

    .shape-3 {
        width: 80px;
        height: 80px;
        bottom: 20%;
        left: 50%;
        animation-delay: 2s;
    }

    @keyframes float {

        0%,
        100% {
            transform: translateY(0px) rotate(0deg);
        }

        50% {
            transform: translateY(-20px) rotate(180deg);
        }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .auth-content {
            grid-template-columns: 1fr;
            gap: var(--spacing-lg);
        }

        .auth-card {
            padding: var(--spacing-lg);
        }

        .auth-visual {
            width: 300px;
            height: 300px;
        }

        .auth-illustration {
            font-size: 80px;
        }

        .form-options {
            flex-direction: column;
            gap: var(--spacing-md);
            align-items: flex-start;
        }
    }

    @media (max-width: 480px) {
        .auth-container {
            padding: var(--spacing-md);
        }

        .auth-card {
            padding: var(--spacing-md);
        }

        .auth-title {
            font-size: var(--font-size-2xl);
        }

        .social-auth {
            gap: var(--spacing-sm);
        }

        .social-btn {
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: var(--font-size-sm);
        }
    }
</style>

<script>
    function togglePassword() {
        const passwordInput = document.getElementById('password');
        const toggleBtn = document.querySelector('.password-toggle i');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleBtn.className = 'fas fa-eye-slash';
        } else {
            passwordInput.type = 'password';
            toggleBtn.className = 'fas fa-eye';
        }
    }

    function socialLogin(provider) {
        // Implement social login functionality
        showNotification(`Logging in with ${provider}...`, 'info');
        // Redirect to social login URL
        // window.location.href = `/auth/${provider}`;
    }

    // Form validation
    document.querySelector('.auth-form').addEventListener('submit', function(e) {
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        if (!email || !password) {
            e.preventDefault();
            showNotification('Please fill in all fields', 'error');
            return;
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            e.preventDefault();
            showNotification('Please enter a valid email address', 'error');
            return;
        }

        // Show loading state
        const submitBtn = document.querySelector('.auth-submit');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing In...';
    });

    // Auto-focus on email field
    document.getElementById('email').focus();
</script>